import React from 'react';

import { DndProvider } from 'react-dnd';
import { HTML5Backend } from 'react-dnd-html5-backend';
import {
  HashRouter as Router,
  Navigate,
  Route,
  Routes,
} from 'react-router-dom';

import EmailEditor from './components/EmailEditor'; // <-- Import EmailEditor
import Layout from './components/Layout'; // Ensure Layout is imported
import {
  AuthProvider,
  useAuth,
} from './contexts/AuthContext';
import CampaignSummary
  from './pages/campaigns/CampaignSummary'; // Restore direct import
import Login from './pages/Login'; // Import directly
import Register from './pages/Register'; // Import directly

// Lazy load components for better performance
const Dashboard = React.lazy(() => import('./pages/Dashboard'));
const Templates = React.lazy(() => import('./pages/Templates'));
const TemplateForm = React.lazy(() => import('./pages/templates/TemplateForm'));
// Comment out the lazy import of AITemplateGenerator
// const AITemplateGenerator = React.lazy(() => import('./pages/templates/AITemplateGenerator'));
const Contacts = React.lazy(() => import('./pages/Contacts')); // Added Contacts import
const CampaignList = React.lazy(() => import('./pages/campaigns/CampaignList'));
const CampaignCreate = React.lazy(() => import('./pages/campaigns/CampaignCreate'));
const CampaignEdit = React.lazy(() => import('./pages/campaigns/CampaignEdit'));
const CampaignAnalytics = React.lazy(() => import('./pages/campaigns/CampaignAnalytics'));
const CampaignRecipients = React.lazy(() => import('./pages/campaigns/CampaignRecipients'));
const CampaignSchedule = React.lazy(() => import('./pages/campaigns/CampaignSchedule'));
const DomainSetup = React.lazy(() => import('./pages/campaigns/DomainSetup'));
const Analytics = React.lazy(() => import('./pages/Analytics'));
const Automations = React.lazy(() => import('./pages/Automations'));
const Settings = React.lazy(() => import('./pages/Settings'));
const Billing = React.lazy(() => import('./pages/Billing'));
const Support = React.lazy(() => import('./pages/Support'));
const AIContentGenerator = React.lazy(() => import('./pages/AIContentGenerator'));
const PersonalizationEditor = React.lazy(() => import('./pages/PersonalizationEditor'));
const InteractiveElements = React.lazy(() => import('./pages/InteractiveElements'));
const SendTimeOptimization = React.lazy(() => import('./pages/SendTimeOptimization'));
const ABTesting = React.lazy(() => import('./pages/ABTesting'));
const SegmentBuilder = React.lazy(() => import('./pages/SegmentBuilder'));
const DeliverabilityDashboard = React.lazy(() => import('./pages/DeliverabilityDashboard'));
const JourneyBuilder = React.lazy(() => import('./pages/JourneyBuilder'));
const IntegrationMarketplace = React.lazy(() => import('./pages/IntegrationMarketplace'));
const MobilePreview = React.lazy(() => import('./pages/MobilePreview'));
const TemplateRecommendations = React.lazy(() => import('./pages/TemplateRecommendations'));
const SchedulingAutomation = React.lazy(() => import('./pages/AdvancedScheduling'));
const DataExportImport = React.lazy(() => import('./pages/DataExportImport'));
const NotFound = React.lazy(() => import('./pages/NotFound'));

// Simple component to directly test useAuth (Optional, can be removed if not needed)
const ContextTester: React.FC = () => {
  try {
    const { loading, isAuthenticated } = useAuth();
    console.log('ContextTester: useAuth() called successfully');
    return (
      <div style={{ position: 'fixed', top: 0, left: 0, backgroundColor: 'lightgreen', padding: '5px', zIndex: 9999 }}>
        Context Test: {loading ? 'Loading...' : isAuthenticated ? 'Authenticated' : 'Not Authenticated'}
      </div>
    );
  } catch (err) {
    console.error('ContextTester: Error calling useAuth()', err);
    return (
      <div style={{ position: 'fixed', top: 0, left: 0, backgroundColor: 'red', color: 'white', padding: '5px', zIndex: 9999 }}>
        Context Test: Error! Check console.
      </div>
    );
  }
};

// Loading component
const Loading = () => (
  <div className="flex items-center justify-center h-screen">
    <div className="animate-spin rounded-full h-12 w-12 border-t-2 border-b-2 border-primary"></div>
  </div>
);

// Protected route component - Modified to include Layout
const ProtectedRoute = ({ children, title }: { children: React.ReactNode, title: string }) => {
  const { isAuthenticated, loading } = useAuth();

  if (loading) {
    return <Loading />;
  }

  if (!isAuthenticated) {
    return <Navigate to="/login" replace />;
  }

  // Render Layout, and wrap children in Suspense *inside* the Layout
  return (
    <Layout title={title}>
      <React.Suspense fallback={<Loading />}>
        {children}
      </React.Suspense>
    </Layout>
  );
};

const App: React.FC = () => {
  return (
    <AuthProvider>
      <DndProvider backend={HTML5Backend}>
        <Router>
          <Routes>
            {/* Public routes rendered directly */}
            <Route path="/login" element={<Login />} />
            <Route path="/register" element={<Register />} />

            {/* Protected routes use ProtectedRoute */}
            <Route
              path="/"
              element={<ProtectedRoute title="Dashboard"><Dashboard /></ProtectedRoute>}
            />
            <Route
              path="/email-templates"
              element={<ProtectedRoute title="Templates"><Templates /></ProtectedRoute>}
            />
            <Route
              path="/email-templates/create"
              element={<ProtectedRoute title="Create Template"><EmailEditor /></ProtectedRoute>}
            />
            {/* Comment out the route for AITemplateGenerator */}
            {/* <Route
              path="/ai-template-generator"
              element={<ProtectedRoute title="AI Template Generator"><AITemplateGenerator /></ProtectedRoute>}
            /> */}
            <Route
              path="/email-templates/editor/:templateId"
              element={<ProtectedRoute title="Edit Template"><EmailEditor /></ProtectedRoute>}
            />
            <Route
              path="/contacts"
              element={<ProtectedRoute title="Contacts"><Contacts /></ProtectedRoute>}
            />
            <Route
              path="/campaigns"
              element={<ProtectedRoute title="Campaigns"><CampaignList /></ProtectedRoute>}
            />
            <Route
              path="/campaigns/create"
              element={<ProtectedRoute title="Create Campaign"><CampaignCreate /></ProtectedRoute>}
            />
            <Route
              path="/campaigns/domain-setup"
              element={<ProtectedRoute title="Domain Setup"><DomainSetup /></ProtectedRoute>}
            />
            <Route
              path="/campaigns/edit/:id"
              element={<ProtectedRoute title="Edit Campaign"><CampaignEdit /></ProtectedRoute>}
            />
            <Route
              path="/campaigns/analytics/:id"
              element={<ProtectedRoute title="Campaign Analytics"><CampaignAnalytics /></ProtectedRoute>}
            />
            <Route
              path="/campaigns/recipients/:id"
              element={<ProtectedRoute title="Campaign Recipients"><CampaignRecipients /></ProtectedRoute>}
            />
            <Route
              path="/campaigns/:id/schedule"
              element={<ProtectedRoute title="Campaign Schedule"><CampaignSchedule /></ProtectedRoute>}
            />
            <Route
              path="/campaign-summary/:id"
              element={<ProtectedRoute title="Campaign Summary"><CampaignSummary /></ProtectedRoute>}
            />
            <Route
              path="/analytics"
              element={<ProtectedRoute title="Analytics"><Analytics /></ProtectedRoute>}
            />
            <Route
              path="/automations"
              element={<ProtectedRoute title="Automations"><Automations /></ProtectedRoute>}
            />
            <Route
              path="/settings"
              element={<ProtectedRoute title="Settings"><Settings /></ProtectedRoute>}
            />
            <Route
              path="/billing"
              element={<ProtectedRoute title="Billing"><Billing /></ProtectedRoute>}
            />
            <Route
              path="/support"
              element={<ProtectedRoute title="Support"><Support /></ProtectedRoute>}
            />
            {/* Advanced Features Routes */}
            <Route
              path="/ai-content-generator"
              element={<ProtectedRoute title="AI Content Generator"><AIContentGenerator /></ProtectedRoute>}
            />
            <Route
              path="/personalization-editor"
              element={<ProtectedRoute title="Personalization Editor"><PersonalizationEditor /></ProtectedRoute>}
            />
            <Route
              path="/interactive-elements"
              element={<ProtectedRoute title="Interactive Elements"><InteractiveElements /></ProtectedRoute>}
            />
            <Route
              path="/send-time-optimization"
              element={<ProtectedRoute title="Send Time Optimization"><SendTimeOptimization /></ProtectedRoute>}
            />
            <Route
              path="/ab-testing"
              element={<ProtectedRoute title="AB Testing"><ABTesting /></ProtectedRoute>}
            />
            <Route
              path="/segment-builder"
              element={<ProtectedRoute title="Segment Builder"><SegmentBuilder /></ProtectedRoute>}
            />
            <Route
              path="/deliverability-dashboard"
              element={<ProtectedRoute title="Deliverability Dashboard"><DeliverabilityDashboard /></ProtectedRoute>}
            />
            <Route
              path="/journey-builder"
              element={<ProtectedRoute title="Journey Builder"><JourneyBuilder /></ProtectedRoute>}
            />
            <Route
              path="/integration-marketplace"
              element={<ProtectedRoute title="Integration Marketplace"><IntegrationMarketplace /></ProtectedRoute>}
            />
            <Route
              path="/mobile-preview"
              element={<ProtectedRoute title="Mobile Preview"><MobilePreview /></ProtectedRoute>}
            />
            <Route
              path="/template-recommendations"
              element={<ProtectedRoute title="Template Recommendations"><TemplateRecommendations /></ProtectedRoute>}
            />
            <Route
              path="/scheduling-automation"
              element={<ProtectedRoute title="Scheduling Automation"><SchedulingAutomation /></ProtectedRoute>}
            />
            <Route
              path="/data-export-import"
              element={<ProtectedRoute title="Data Export/Import"><DataExportImport /></ProtectedRoute>}
            />

            {/* --- New Email Editor Routes --- */}
            <Route
              path="/email-editor"
              element={<ProtectedRoute title="Create Email Template"><EmailEditor /></ProtectedRoute>}
            />
            <Route
              path="/email-editor/:templateId"
              element={<ProtectedRoute title="Edit Email Template"><EmailEditor /></ProtectedRoute>}
            />
            {/* --- End New Email Editor Routes --- */}

            {/* Restore Catch-all route for 404 */}
            <Route
              path="*"
              element={<ProtectedRoute title="Not Found"><NotFound /></ProtectedRoute>}
            />
          </Routes>
        </Router>
      </DndProvider>
    </AuthProvider>
  );
};

export default App;
