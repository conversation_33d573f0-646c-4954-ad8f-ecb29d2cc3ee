/**
 * SaveTemplateModal component for Driftly Email Generator
 * Modal dialog for saving email templates
 */

import React, { useState } from 'react';

interface SaveTemplateModalProps {
  initialName: string;
  onSave: (name: string, isPublic: boolean) => Promise<any>;
  onCancel: () => void;
  isSaving: boolean;
  error?: string | null;
}

const SaveTemplateModal: React.FC<SaveTemplateModalProps> = ({
  initialName,
  onSave,
  onCancel,
  isSaving,
  error: externalError
}) => {
  const [templateName, setTemplateName] = useState<string>(initialName || 'Untitled Template');
  const [isPublic, setIsPublic] = useState<boolean>(false);
  const [internalError, setInternalError] = useState<string | null>(null);

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();

    if (!templateName.trim()) {
      setInternalError('Template name is required');
      return;
    }

    try {
      await onSave(templateName, isPublic);
    } catch (err) {
      setInternalError('Failed to save template. Please try again.');
    }
  };

  // Use external error if provided, otherwise use internal error
  const displayError = externalError || internalError;

  return (
    <div className="modal-overlay">
      <div className="modal-container">
        <div className="modal-header">
          <h3>Save Template</h3>
          <button className="close-button" onClick={onCancel} disabled={isSaving}>
            <svg className="w-5 h-5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
              <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M6 18L18 6M6 6l12 12" />
            </svg>
          </button>
        </div>

        <form onSubmit={handleSubmit}>
          <div className="modal-body">
            {displayError && (
              <div className="error-message">
                <svg className="w-5 h-5 mr-2 inline" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                  <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M12 8v4m0 4h.01M21 12a9 9 0 11-18 0 9 9 0 0118 0z" />
                </svg>
                {displayError}
              </div>
            )}

            <div className="form-group">
              <label htmlFor="templateName">Template Name</label>
              <input
                type="text"
                id="templateName"
                value={templateName}
                onChange={(e) => setTemplateName(e.target.value)}
                className="form-control"
                placeholder="Enter template name"
                required
                disabled={isSaving}
              />
            </div>

            <div className="form-group checkbox">
              <input
                type="checkbox"
                id="isPublic"
                checked={isPublic}
                onChange={(e) => setIsPublic(e.target.checked)}
                className="form-checkbox"
                disabled={isSaving}
              />
              <label htmlFor="isPublic">Make template public</label>
            </div>
          </div>

          <div className="modal-footer">
            <button
              type="button"
              className="cancel-button"
              onClick={onCancel}
              disabled={isSaving}
            >
              Cancel
            </button>
            <button
              type="submit"
              className="save-button"
              disabled={isSaving}
            >
              {isSaving ? (
                <>
                  <svg className="w-4 h-4 mr-2 animate-spin" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M4 4v5h.582m15.356 2A8.001 8.001 0 004.582 9m0 0H9m11 11v-5h-.581m0 0a8.003 8.003 0 01-15.357-2m15.357 2H15" />
                  </svg>
                  Saving...
                </>
              ) : (
                <>
                  <svg className="w-4 h-4 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M8 7H5a2 2 0 00-2 2v9a2 2 0 002 2h14a2 2 0 002-2V9a2 2 0 00-2-2h-3m-1 4l-3 3m0 0l-3-3m3 3V4" />
                  </svg>
                  Save Template
                </>
              )}
            </button>
          </div>
        </form>
      </div>
    </div>
  );
};

export default SaveTemplateModal;