{"ast": null, "code": "var _jsxFileName = \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\DONT DELETE\\\\driftly-enhanced-current-2.0.0\\\\frontend\\\\src\\\\components\\\\BlockLibrary.tsx\",\n  _s = $RefreshSig$(),\n  _s2 = $RefreshSig$();\n/**\n * Enhanced BlockLibrary component for Driftly Email Generator\n * Displays available blocks with improved UI, categorization, and drag feedback\n */\n\nimport React, { useCallback, useMemo, useState } from 'react';\nimport { useDrag } from 'react-dnd';\n\n// Import Block interface from types to ensure consistency\nimport { jsxDEV as _jsxDEV } from \"react/jsx-dev-runtime\";\n// Define ItemTypes if not imported elsewhere\nconst ItemTypes = {\n  LIBRARY_BLOCK: 'library_block'\n};\n// Helper function to get category colors with Driftly theme\nconst getCategoryColor = category => {\n  const colors = {\n    'Header': 'bg-gradient-to-r from-purple-500 to-purple-600 text-white',\n    'Content': 'bg-gradient-to-r from-blue-500 to-blue-600 text-white',\n    'Layout': 'bg-gradient-to-r from-green-500 to-green-600 text-white',\n    'Footer': 'bg-gradient-to-r from-orange-500 to-orange-600 text-white',\n    'CTA': 'bg-gradient-to-r from-red-500 to-red-600 text-white',\n    'Social': 'bg-gradient-to-r from-pink-500 to-pink-600 text-white',\n    'Product': 'bg-gradient-to-r from-yellow-500 to-yellow-600 text-white',\n    'Navigation': 'bg-gradient-to-r from-indigo-500 to-indigo-600 text-white'\n  };\n  return colors[category] || 'bg-gradient-to-r from-gray-500 to-gray-600 text-white';\n};\n\n// Helper function to get category icons\nconst getCategoryIcon = category => {\n  const icons = {\n    'Header': '🏠',\n    'Content': '📝',\n    'Layout': '📐',\n    'Footer': '🦶',\n    'CTA': '🎯',\n    'Social': '📱',\n    'Product': '🛍️',\n    'Navigation': '🧭'\n  };\n  return icons[category] || '📦';\n};\n\n// Component that renders each individual library block\nconst LibraryBlock = /*#__PURE__*/_s(/*#__PURE__*/React.memo(_c = _s(({\n  block,\n  onAddBlock,\n  isVisible\n}) => {\n  _s();\n  const [{\n    isDragging\n  }, drag] = useDrag(() => ({\n    type: ItemTypes.LIBRARY_BLOCK,\n    item: {\n      block,\n      type: ItemTypes.LIBRARY_BLOCK\n    },\n    collect: monitor => ({\n      isDragging: monitor.isDragging()\n    })\n  }), [block]);\n\n  // Don't render content if block is not visible\n  if (!isVisible) {\n    return /*#__PURE__*/_jsxDEV(\"div\", {\n      className: \"h-20 bg-gray-100 animate-pulse rounded\"\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 73,\n      columnNumber: 12\n    }, this); // Loading placeholder\n  }\n  return /*#__PURE__*/_jsxDEV(\"div\", {\n    ref: drag,\n    className: `library-block ${isDragging ? 'dragging' : ''}`,\n    onClick: () => onAddBlock(block),\n    children: [/*#__PURE__*/_jsxDEV(\"div\", {\n      className: \"flex items-center justify-between mb-3\",\n      children: [/*#__PURE__*/_jsxDEV(\"h5\", {\n        className: \"block-name truncate\",\n        children: block.name\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 83,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n        className: `text-xs px-2 py-1 rounded-full font-semibold shadow-sm ${getCategoryColor(block.category)}`,\n        children: [getCategoryIcon(block.category), \" \", block.category]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 84,\n        columnNumber: 9\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 82,\n      columnNumber: 7\n    }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n      className: \"block-preview\",\n      children: block.thumbnail ? /*#__PURE__*/_jsxDEV(\"img\", {\n        src: block.thumbnail,\n        alt: block.name,\n        className: \"w-full h-full object-cover\",\n        loading: \"lazy\"\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 93,\n        columnNumber: 11\n      }, this) : /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"text-center\",\n        children: /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"block-type-icon\",\n          children: getCategoryIcon(block.category)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 101,\n          columnNumber: 13\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 100,\n        columnNumber: 11\n      }, this)\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 91,\n      columnNumber: 7\n    }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n      className: \"block-info\",\n      children: block.description && /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"block-description line-clamp-2\",\n        children: block.description\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 110,\n        columnNumber: 11\n      }, this)\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 108,\n      columnNumber: 7\n    }, this), isDragging && /*#__PURE__*/_jsxDEV(\"div\", {\n      className: \"absolute inset-0 bg-gradient-to-br from-primary-blue/20 to-accent-coral/20 rounded-lg flex items-center justify-center backdrop-blur-sm\",\n      children: /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"text-accent-coral font-semibold text-sm flex items-center\",\n        children: [/*#__PURE__*/_jsxDEV(\"svg\", {\n          className: \"w-4 h-4 mr-2 animate-pulse\",\n          fill: \"none\",\n          stroke: \"currentColor\",\n          viewBox: \"0 0 24 24\",\n          children: /*#__PURE__*/_jsxDEV(\"path\", {\n            strokeLinecap: \"round\",\n            strokeLinejoin: \"round\",\n            strokeWidth: 2,\n            d: \"M7 16V4m0 0L3 8m4-4l4 4m6 0v12m0 0l4-4m-4 4l-4-4\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 121,\n            columnNumber: 15\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 120,\n          columnNumber: 13\n        }, this), \"Dragging...\"]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 119,\n        columnNumber: 11\n      }, this)\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 118,\n      columnNumber: 9\n    }, this)]\n  }, void 0, true, {\n    fileName: _jsxFileName,\n    lineNumber: 77,\n    columnNumber: 5\n  }, this);\n}, \"wuumsFp4qAni9XRJfRhQAZjuD/k=\", false, function () {\n  return [useDrag];\n})), \"wuumsFp4qAni9XRJfRhQAZjuD/k=\", false, function () {\n  return [useDrag];\n});\n\n// Main component for block library\n_c2 = LibraryBlock;\nconst BlockLibrary = ({\n  blocks,\n  onAddBlock\n}) => {\n  _s2();\n  const [activeCategory, setActiveCategory] = useState('');\n  const [searchTerm, setSearchTerm] = useState('');\n  const [visibleBlocks, setVisibleBlocks] = useState(new Set());\n\n  // Get unique categories for filter tabs\n  const categories = useMemo(() => {\n    const cats = new Set();\n    blocks.forEach(block => {\n      if (block.category) cats.add(block.category);\n    });\n    return ['All', ...Array.from(cats)];\n  }, [blocks]);\n\n  // Filter blocks based on category and search term\n  const filteredBlocks = useMemo(() => {\n    return blocks.filter(block => {\n      // Category filter\n      if (activeCategory && activeCategory !== 'All' && block.category !== activeCategory) {\n        return false;\n      }\n\n      // Search filter\n      if (searchTerm.trim() !== '') {\n        const searchLower = searchTerm.toLowerCase();\n        return block.name.toLowerCase().includes(searchLower) || (block.description || '').toLowerCase().includes(searchLower) || (block.category || '').toLowerCase().includes(searchLower);\n      }\n      return true;\n    });\n  }, [blocks, activeCategory, searchTerm]);\n\n  // Implement intersection observer for lazy loading\n  const blockObserver = useCallback(node => {\n    if (!node) return;\n    const observer = new IntersectionObserver(entries => {\n      entries.forEach(entry => {\n        if (entry.isIntersecting) {\n          const blockId = entry.target.getAttribute('data-block-id');\n          if (blockId) {\n            // Fix Set iteration by using current value to create a new Set\n            setVisibleBlocks(prev => {\n              const newSet = new Set(prev);\n              newSet.add(blockId);\n              return newSet;\n            });\n          }\n        }\n      });\n    }, {\n      rootMargin: '200px 0px'\n    } // Load blocks that are 200px outside viewport\n    );\n    observer.observe(node);\n    return () => observer.disconnect();\n  }, []);\n\n  // Cache check for block visibility\n  const isBlockVisible = useCallback(blockId => {\n    return visibleBlocks.has(blockId);\n  }, [visibleBlocks]);\n  return /*#__PURE__*/_jsxDEV(\"div\", {\n    className: \"block-library\",\n    children: [/*#__PURE__*/_jsxDEV(\"div\", {\n      className: \"search-container\",\n      children: /*#__PURE__*/_jsxDEV(\"input\", {\n        type: \"search\",\n        placeholder: \"Search blocks...\",\n        className: \"search-input\",\n        value: searchTerm,\n        onChange: e => setSearchTerm(e.target.value)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 205,\n        columnNumber: 9\n      }, this)\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 204,\n      columnNumber: 7\n    }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n      className: \"category-tabs\",\n      children: categories.map(category => /*#__PURE__*/_jsxDEV(\"button\", {\n        className: `category-tab ${activeCategory === category || category === 'All' && !activeCategory ? 'active' : ''}`,\n        onClick: () => setActiveCategory(category === 'All' ? '' : category),\n        children: category === 'All' ? '🎨 All' : `${getCategoryIcon(category)} ${category}`\n      }, category, false, {\n        fileName: _jsxFileName,\n        lineNumber: 217,\n        columnNumber: 11\n      }, this))\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 215,\n      columnNumber: 7\n    }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n      className: \"blocks-grid\",\n      children: filteredBlocks.length > 0 ? filteredBlocks.map(block => /*#__PURE__*/_jsxDEV(\"div\", {\n        ref: blockObserver,\n        \"data-block-id\": block.blockId || block._id || `block-${Math.random()}`,\n        children: /*#__PURE__*/_jsxDEV(LibraryBlock, {\n          block: block,\n          onAddBlock: onAddBlock,\n          isVisible: isBlockVisible(block.blockId || block._id || '')\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 240,\n          columnNumber: 15\n        }, this)\n      }, block.blockId || block._id || `block-${Math.random()}`, false, {\n        fileName: _jsxFileName,\n        lineNumber: 235,\n        columnNumber: 13\n      }, this)) : /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"no-blocks\",\n        children: /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"text-center\",\n          children: [/*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"text-4xl mb-2\",\n            children: \"\\uD83D\\uDD0D\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 250,\n            columnNumber: 15\n          }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n            children: \"No blocks found.\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 251,\n            columnNumber: 15\n          }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"text-xs mt-1\",\n            children: \"Try a different search or category.\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 252,\n            columnNumber: 15\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 249,\n          columnNumber: 13\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 248,\n        columnNumber: 11\n      }, this)\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 232,\n      columnNumber: 7\n    }, this)]\n  }, void 0, true, {\n    fileName: _jsxFileName,\n    lineNumber: 202,\n    columnNumber: 5\n  }, this);\n};\n_s2(BlockLibrary, \"GomSnj/dpTzNhBMlHfUCUS9rOqQ=\");\n_c3 = BlockLibrary;\nexport default _c4 = /*#__PURE__*/React.memo(BlockLibrary);\nexport { BlockLibrary };\nvar _c, _c2, _c3, _c4;\n$RefreshReg$(_c, \"LibraryBlock$React.memo\");\n$RefreshReg$(_c2, \"LibraryBlock\");\n$RefreshReg$(_c3, \"BlockLibrary\");\n$RefreshReg$(_c4, \"%default%\");", "map": {"version": 3, "names": ["React", "useCallback", "useMemo", "useState", "useDrag", "jsxDEV", "_jsxDEV", "ItemTypes", "LIBRARY_BLOCK", "getCategoryColor", "category", "colors", "getCategoryIcon", "icons", "LibraryBlock", "_s", "memo", "_c", "block", "onAddBlock", "isVisible", "isDragging", "drag", "type", "item", "collect", "monitor", "className", "fileName", "_jsxFileName", "lineNumber", "columnNumber", "ref", "onClick", "children", "name", "thumbnail", "src", "alt", "loading", "description", "fill", "stroke", "viewBox", "strokeLinecap", "strokeLinejoin", "strokeWidth", "d", "_c2", "BlockLibrary", "blocks", "_s2", "activeCategory", "setActiveCategory", "searchTerm", "setSearchTerm", "visibleBlocks", "setVisibleBlocks", "Set", "categories", "cats", "for<PERSON>ach", "add", "Array", "from", "filteredBlocks", "filter", "trim", "searchLower", "toLowerCase", "includes", "blockObserver", "node", "observer", "IntersectionObserver", "entries", "entry", "isIntersecting", "blockId", "target", "getAttribute", "prev", "newSet", "rootMargin", "observe", "disconnect", "isBlockVisible", "has", "placeholder", "value", "onChange", "e", "map", "length", "_id", "Math", "random", "_c3", "_c4", "$RefreshReg$"], "sources": ["C:/Users/<USER>/OneDrive/Desktop/DONT DELETE/driftly-enhanced-current-2.0.0/frontend/src/components/BlockLibrary.tsx"], "sourcesContent": ["/**\n * Enhanced BlockLibrary component for Driftly Email Generator\n * Displays available blocks with improved UI, categorization, and drag feedback\n */\n\nimport React, {\n  useCallback,\n  useMemo,\n  useState,\n} from 'react';\n\nimport { useDrag } from 'react-dnd';\n\n// Import Block interface from types to ensure consistency\nimport { Block } from '../types/editor';\n\n// Define ItemTypes if not imported elsewhere\nconst ItemTypes = {\n  LIBRARY_BLOCK: 'library_block'\n};\n\ninterface BlockLibraryProps {\n  blocks: Block[];\n  onAddBlock: (block: Block) => void;\n}\n\n// Helper function to get category colors with Driftly theme\nconst getCategoryColor = (category: string): string => {\n  const colors: Record<string, string> = {\n    'Header': 'bg-gradient-to-r from-purple-500 to-purple-600 text-white',\n    'Content': 'bg-gradient-to-r from-blue-500 to-blue-600 text-white',\n    'Layout': 'bg-gradient-to-r from-green-500 to-green-600 text-white',\n    'Footer': 'bg-gradient-to-r from-orange-500 to-orange-600 text-white',\n    'CTA': 'bg-gradient-to-r from-red-500 to-red-600 text-white',\n    'Social': 'bg-gradient-to-r from-pink-500 to-pink-600 text-white',\n    'Product': 'bg-gradient-to-r from-yellow-500 to-yellow-600 text-white',\n    'Navigation': 'bg-gradient-to-r from-indigo-500 to-indigo-600 text-white',\n  };\n  return colors[category] || 'bg-gradient-to-r from-gray-500 to-gray-600 text-white';\n};\n\n// Helper function to get category icons\nconst getCategoryIcon = (category: string): string => {\n  const icons: Record<string, string> = {\n    'Header': '🏠',\n    'Content': '📝',\n    'Layout': '📐',\n    'Footer': '🦶',\n    'CTA': '🎯',\n    'Social': '📱',\n    'Product': '🛍️',\n    'Navigation': '🧭',\n  };\n  return icons[category] || '📦';\n};\n\n// Component that renders each individual library block\nconst LibraryBlock: React.FC<{\n  block: Block,\n  onAddBlock: (block: Block) => void,\n  isVisible: boolean\n}> = React.memo(({ block, onAddBlock, isVisible }) => {\n  const [{ isDragging }, drag] = useDrag(() => ({\n    type: ItemTypes.LIBRARY_BLOCK,\n    item: { block, type: ItemTypes.LIBRARY_BLOCK },\n    collect: (monitor) => ({\n      isDragging: monitor.isDragging()\n    })\n  }), [block]);\n\n  // Don't render content if block is not visible\n  if (!isVisible) {\n    return <div className=\"h-20 bg-gray-100 animate-pulse rounded\" />; // Loading placeholder\n  }\n\n  return (\n    <div\n      ref={drag}\n      className={`library-block ${isDragging ? 'dragging' : ''}`}\n      onClick={() => onAddBlock(block)}\n    >\n      <div className=\"flex items-center justify-between mb-3\">\n        <h5 className=\"block-name truncate\">{block.name}</h5>\n        <span className={`text-xs px-2 py-1 rounded-full font-semibold shadow-sm ${\n          getCategoryColor(block.category)\n        }`}>\n          {getCategoryIcon(block.category)} {block.category}\n        </span>\n      </div>\n\n      <div className=\"block-preview\">\n        {block.thumbnail ? (\n          <img\n            src={block.thumbnail}\n            alt={block.name}\n            className=\"w-full h-full object-cover\"\n            loading=\"lazy\"\n          />\n        ) : (\n          <div className=\"text-center\">\n            <div className=\"block-type-icon\">\n              {getCategoryIcon(block.category)}\n            </div>\n          </div>\n        )}\n      </div>\n\n      <div className=\"block-info\">\n        {block.description && (\n          <div className=\"block-description line-clamp-2\">\n            {block.description}\n          </div>\n        )}\n      </div>\n\n      {/* Enhanced drag indicator */}\n      {isDragging && (\n        <div className=\"absolute inset-0 bg-gradient-to-br from-primary-blue/20 to-accent-coral/20 rounded-lg flex items-center justify-center backdrop-blur-sm\">\n          <div className=\"text-accent-coral font-semibold text-sm flex items-center\">\n            <svg className=\"w-4 h-4 mr-2 animate-pulse\" fill=\"none\" stroke=\"currentColor\" viewBox=\"0 0 24 24\">\n              <path strokeLinecap=\"round\" strokeLinejoin=\"round\" strokeWidth={2} d=\"M7 16V4m0 0L3 8m4-4l4 4m6 0v12m0 0l4-4m-4 4l-4-4\" />\n            </svg>\n            Dragging...\n          </div>\n        </div>\n      )}\n    </div>\n  );\n});\n\n// Main component for block library\nconst BlockLibrary: React.FC<BlockLibraryProps> = ({ blocks, onAddBlock }) => {\n  const [activeCategory, setActiveCategory] = useState<string>('');\n  const [searchTerm, setSearchTerm] = useState<string>('');\n  const [visibleBlocks, setVisibleBlocks] = useState<Set<string>>(new Set());\n\n  // Get unique categories for filter tabs\n  const categories = useMemo(() => {\n    const cats = new Set<string>();\n    blocks.forEach(block => {\n      if (block.category) cats.add(block.category);\n    });\n    return ['All', ...Array.from(cats)];\n  }, [blocks]);\n\n  // Filter blocks based on category and search term\n  const filteredBlocks = useMemo(() => {\n    return blocks.filter(block => {\n      // Category filter\n      if (activeCategory && activeCategory !== 'All' && block.category !== activeCategory) {\n        return false;\n      }\n\n      // Search filter\n      if (searchTerm.trim() !== '') {\n        const searchLower = searchTerm.toLowerCase();\n        return (\n          block.name.toLowerCase().includes(searchLower) ||\n          (block.description || '').toLowerCase().includes(searchLower) ||\n          (block.category || '').toLowerCase().includes(searchLower)\n        );\n      }\n\n      return true;\n    });\n  }, [blocks, activeCategory, searchTerm]);\n\n  // Implement intersection observer for lazy loading\n  const blockObserver = useCallback((node: HTMLDivElement | null) => {\n    if (!node) return;\n\n    const observer = new IntersectionObserver(\n      (entries) => {\n        entries.forEach(entry => {\n          if (entry.isIntersecting) {\n            const blockId = entry.target.getAttribute('data-block-id');\n            if (blockId) {\n              // Fix Set iteration by using current value to create a new Set\n              setVisibleBlocks(prev => {\n                const newSet = new Set(prev);\n                newSet.add(blockId);\n                return newSet;\n              });\n            }\n          }\n        });\n      },\n      { rootMargin: '200px 0px' } // Load blocks that are 200px outside viewport\n    );\n\n    observer.observe(node);\n\n    return () => observer.disconnect();\n  }, []);\n\n  // Cache check for block visibility\n  const isBlockVisible = useCallback((blockId: string) => {\n    return visibleBlocks.has(blockId);\n  }, [visibleBlocks]);\n\n  return (\n    <div className=\"block-library\">\n      {/* Enhanced Search Box */}\n      <div className=\"search-container\">\n        <input\n          type=\"search\"\n          placeholder=\"Search blocks...\"\n          className=\"search-input\"\n          value={searchTerm}\n          onChange={(e) => setSearchTerm(e.target.value)}\n        />\n      </div>\n\n      {/* Enhanced Category Tabs */}\n      <div className=\"category-tabs\">\n        {categories.map((category) => (\n          <button\n            key={category}\n            className={`category-tab ${\n              activeCategory === category || (category === 'All' && !activeCategory)\n                ? 'active'\n                : ''\n            }`}\n            onClick={() => setActiveCategory(category === 'All' ? '' : category)}\n          >\n            {category === 'All' ? '🎨 All' : `${getCategoryIcon(category)} ${category}`}\n          </button>\n        ))}\n      </div>\n\n      {/* Enhanced Block List */}\n      <div className=\"blocks-grid\">\n        {filteredBlocks.length > 0 ? (\n          filteredBlocks.map((block) => (\n            <div\n              key={block.blockId || block._id || `block-${Math.random()}`}\n              ref={blockObserver}\n              data-block-id={block.blockId || block._id || `block-${Math.random()}`}\n            >\n              <LibraryBlock\n                block={block}\n                onAddBlock={onAddBlock}\n                isVisible={isBlockVisible(block.blockId || block._id || '')}\n              />\n            </div>\n          ))\n        ) : (\n          <div className=\"no-blocks\">\n            <div className=\"text-center\">\n              <div className=\"text-4xl mb-2\">🔍</div>\n              <div>No blocks found.</div>\n              <div className=\"text-xs mt-1\">Try a different search or category.</div>\n            </div>\n          </div>\n        )}\n      </div>\n    </div>\n  );\n};\n\nexport default React.memo(BlockLibrary);\nexport { BlockLibrary };\n"], "mappings": ";;;AAAA;AACA;AACA;AACA;;AAEA,OAAOA,KAAK,IACVC,WAAW,EACXC,OAAO,EACPC,QAAQ,QACH,OAAO;AAEd,SAASC,OAAO,QAAQ,WAAW;;AAEnC;AAAA,SAAAC,MAAA,IAAAC,OAAA;AAGA;AACA,MAAMC,SAAS,GAAG;EAChBC,aAAa,EAAE;AACjB,CAAC;AAOD;AACA,MAAMC,gBAAgB,GAAIC,QAAgB,IAAa;EACrD,MAAMC,MAA8B,GAAG;IACrC,QAAQ,EAAE,2DAA2D;IACrE,SAAS,EAAE,uDAAuD;IAClE,QAAQ,EAAE,yDAAyD;IACnE,QAAQ,EAAE,2DAA2D;IACrE,KAAK,EAAE,qDAAqD;IAC5D,QAAQ,EAAE,uDAAuD;IACjE,SAAS,EAAE,2DAA2D;IACtE,YAAY,EAAE;EAChB,CAAC;EACD,OAAOA,MAAM,CAACD,QAAQ,CAAC,IAAI,uDAAuD;AACpF,CAAC;;AAED;AACA,MAAME,eAAe,GAAIF,QAAgB,IAAa;EACpD,MAAMG,KAA6B,GAAG;IACpC,QAAQ,EAAE,IAAI;IACd,SAAS,EAAE,IAAI;IACf,QAAQ,EAAE,IAAI;IACd,QAAQ,EAAE,IAAI;IACd,KAAK,EAAE,IAAI;IACX,QAAQ,EAAE,IAAI;IACd,SAAS,EAAE,KAAK;IAChB,YAAY,EAAE;EAChB,CAAC;EACD,OAAOA,KAAK,CAACH,QAAQ,CAAC,IAAI,IAAI;AAChC,CAAC;;AAED;AACA,MAAMI,YAIJ,gBAAAC,EAAA,cAAGf,KAAK,CAACgB,IAAI,CAAAC,EAAA,GAAAF,EAAA,CAAC,CAAC;EAAEG,KAAK;EAAEC,UAAU;EAAEC;AAAU,CAAC,KAAK;EAAAL,EAAA;EACpD,MAAM,CAAC;IAAEM;EAAW,CAAC,EAAEC,IAAI,CAAC,GAAGlB,OAAO,CAAC,OAAO;IAC5CmB,IAAI,EAAEhB,SAAS,CAACC,aAAa;IAC7BgB,IAAI,EAAE;MAAEN,KAAK;MAAEK,IAAI,EAAEhB,SAAS,CAACC;IAAc,CAAC;IAC9CiB,OAAO,EAAGC,OAAO,KAAM;MACrBL,UAAU,EAAEK,OAAO,CAACL,UAAU,CAAC;IACjC,CAAC;EACH,CAAC,CAAC,EAAE,CAACH,KAAK,CAAC,CAAC;;EAEZ;EACA,IAAI,CAACE,SAAS,EAAE;IACd,oBAAOd,OAAA;MAAKqB,SAAS,EAAC;IAAwC;MAAAC,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAAE,CAAC,CAAC,CAAC;EACrE;EAEA,oBACEzB,OAAA;IACE0B,GAAG,EAAEV,IAAK;IACVK,SAAS,EAAE,iBAAiBN,UAAU,GAAG,UAAU,GAAG,EAAE,EAAG;IAC3DY,OAAO,EAAEA,CAAA,KAAMd,UAAU,CAACD,KAAK,CAAE;IAAAgB,QAAA,gBAEjC5B,OAAA;MAAKqB,SAAS,EAAC,wCAAwC;MAAAO,QAAA,gBACrD5B,OAAA;QAAIqB,SAAS,EAAC,qBAAqB;QAAAO,QAAA,EAAEhB,KAAK,CAACiB;MAAI;QAAAP,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAK,CAAC,eACrDzB,OAAA;QAAMqB,SAAS,EAAE,0DACflB,gBAAgB,CAACS,KAAK,CAACR,QAAQ,CAAC,EAC/B;QAAAwB,QAAA,GACAtB,eAAe,CAACM,KAAK,CAACR,QAAQ,CAAC,EAAC,GAAC,EAACQ,KAAK,CAACR,QAAQ;MAAA;QAAAkB,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAC7C,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACJ,CAAC,eAENzB,OAAA;MAAKqB,SAAS,EAAC,eAAe;MAAAO,QAAA,EAC3BhB,KAAK,CAACkB,SAAS,gBACd9B,OAAA;QACE+B,GAAG,EAAEnB,KAAK,CAACkB,SAAU;QACrBE,GAAG,EAAEpB,KAAK,CAACiB,IAAK;QAChBR,SAAS,EAAC,4BAA4B;QACtCY,OAAO,EAAC;MAAM;QAAAX,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACf,CAAC,gBAEFzB,OAAA;QAAKqB,SAAS,EAAC,aAAa;QAAAO,QAAA,eAC1B5B,OAAA;UAAKqB,SAAS,EAAC,iBAAiB;UAAAO,QAAA,EAC7BtB,eAAe,CAACM,KAAK,CAACR,QAAQ;QAAC;UAAAkB,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAC7B;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACH;IACN;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACE,CAAC,eAENzB,OAAA;MAAKqB,SAAS,EAAC,YAAY;MAAAO,QAAA,EACxBhB,KAAK,CAACsB,WAAW,iBAChBlC,OAAA;QAAKqB,SAAS,EAAC,gCAAgC;QAAAO,QAAA,EAC5ChB,KAAK,CAACsB;MAAW;QAAAZ,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACf;IACN;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACE,CAAC,EAGLV,UAAU,iBACTf,OAAA;MAAKqB,SAAS,EAAC,yIAAyI;MAAAO,QAAA,eACtJ5B,OAAA;QAAKqB,SAAS,EAAC,2DAA2D;QAAAO,QAAA,gBACxE5B,OAAA;UAAKqB,SAAS,EAAC,4BAA4B;UAACc,IAAI,EAAC,MAAM;UAACC,MAAM,EAAC,cAAc;UAACC,OAAO,EAAC,WAAW;UAAAT,QAAA,eAC/F5B,OAAA;YAAMsC,aAAa,EAAC,OAAO;YAACC,cAAc,EAAC,OAAO;YAACC,WAAW,EAAE,CAAE;YAACC,CAAC,EAAC;UAAkD;YAAAnB,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAE;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACvH,CAAC,eAER;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAK;IAAC;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACH,CACN;EAAA;IAAAH,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OACE,CAAC;AAEV,CAAC;EAAA,QAlEgC3B,OAAO;AAAA,EAkEvC,CAAC;EAAA,QAlE+BA,OAAO;AAAA,EAkEtC;;AAEF;AAAA4C,GAAA,GAzEMlC,YAIJ;AAsEF,MAAMmC,YAAyC,GAAGA,CAAC;EAAEC,MAAM;EAAE/B;AAAW,CAAC,KAAK;EAAAgC,GAAA;EAC5E,MAAM,CAACC,cAAc,EAAEC,iBAAiB,CAAC,GAAGlD,QAAQ,CAAS,EAAE,CAAC;EAChE,MAAM,CAACmD,UAAU,EAAEC,aAAa,CAAC,GAAGpD,QAAQ,CAAS,EAAE,CAAC;EACxD,MAAM,CAACqD,aAAa,EAAEC,gBAAgB,CAAC,GAAGtD,QAAQ,CAAc,IAAIuD,GAAG,CAAC,CAAC,CAAC;;EAE1E;EACA,MAAMC,UAAU,GAAGzD,OAAO,CAAC,MAAM;IAC/B,MAAM0D,IAAI,GAAG,IAAIF,GAAG,CAAS,CAAC;IAC9BR,MAAM,CAACW,OAAO,CAAC3C,KAAK,IAAI;MACtB,IAAIA,KAAK,CAACR,QAAQ,EAAEkD,IAAI,CAACE,GAAG,CAAC5C,KAAK,CAACR,QAAQ,CAAC;IAC9C,CAAC,CAAC;IACF,OAAO,CAAC,KAAK,EAAE,GAAGqD,KAAK,CAACC,IAAI,CAACJ,IAAI,CAAC,CAAC;EACrC,CAAC,EAAE,CAACV,MAAM,CAAC,CAAC;;EAEZ;EACA,MAAMe,cAAc,GAAG/D,OAAO,CAAC,MAAM;IACnC,OAAOgD,MAAM,CAACgB,MAAM,CAAChD,KAAK,IAAI;MAC5B;MACA,IAAIkC,cAAc,IAAIA,cAAc,KAAK,KAAK,IAAIlC,KAAK,CAACR,QAAQ,KAAK0C,cAAc,EAAE;QACnF,OAAO,KAAK;MACd;;MAEA;MACA,IAAIE,UAAU,CAACa,IAAI,CAAC,CAAC,KAAK,EAAE,EAAE;QAC5B,MAAMC,WAAW,GAAGd,UAAU,CAACe,WAAW,CAAC,CAAC;QAC5C,OACEnD,KAAK,CAACiB,IAAI,CAACkC,WAAW,CAAC,CAAC,CAACC,QAAQ,CAACF,WAAW,CAAC,IAC9C,CAAClD,KAAK,CAACsB,WAAW,IAAI,EAAE,EAAE6B,WAAW,CAAC,CAAC,CAACC,QAAQ,CAACF,WAAW,CAAC,IAC7D,CAAClD,KAAK,CAACR,QAAQ,IAAI,EAAE,EAAE2D,WAAW,CAAC,CAAC,CAACC,QAAQ,CAACF,WAAW,CAAC;MAE9D;MAEA,OAAO,IAAI;IACb,CAAC,CAAC;EACJ,CAAC,EAAE,CAAClB,MAAM,EAAEE,cAAc,EAAEE,UAAU,CAAC,CAAC;;EAExC;EACA,MAAMiB,aAAa,GAAGtE,WAAW,CAAEuE,IAA2B,IAAK;IACjE,IAAI,CAACA,IAAI,EAAE;IAEX,MAAMC,QAAQ,GAAG,IAAIC,oBAAoB,CACtCC,OAAO,IAAK;MACXA,OAAO,CAACd,OAAO,CAACe,KAAK,IAAI;QACvB,IAAIA,KAAK,CAACC,cAAc,EAAE;UACxB,MAAMC,OAAO,GAAGF,KAAK,CAACG,MAAM,CAACC,YAAY,CAAC,eAAe,CAAC;UAC1D,IAAIF,OAAO,EAAE;YACX;YACArB,gBAAgB,CAACwB,IAAI,IAAI;cACvB,MAAMC,MAAM,GAAG,IAAIxB,GAAG,CAACuB,IAAI,CAAC;cAC5BC,MAAM,CAACpB,GAAG,CAACgB,OAAO,CAAC;cACnB,OAAOI,MAAM;YACf,CAAC,CAAC;UACJ;QACF;MACF,CAAC,CAAC;IACJ,CAAC,EACD;MAAEC,UAAU,EAAE;IAAY,CAAC,CAAC;IAC9B,CAAC;IAEDV,QAAQ,CAACW,OAAO,CAACZ,IAAI,CAAC;IAEtB,OAAO,MAAMC,QAAQ,CAACY,UAAU,CAAC,CAAC;EACpC,CAAC,EAAE,EAAE,CAAC;;EAEN;EACA,MAAMC,cAAc,GAAGrF,WAAW,CAAE6E,OAAe,IAAK;IACtD,OAAOtB,aAAa,CAAC+B,GAAG,CAACT,OAAO,CAAC;EACnC,CAAC,EAAE,CAACtB,aAAa,CAAC,CAAC;EAEnB,oBACElD,OAAA;IAAKqB,SAAS,EAAC,eAAe;IAAAO,QAAA,gBAE5B5B,OAAA;MAAKqB,SAAS,EAAC,kBAAkB;MAAAO,QAAA,eAC/B5B,OAAA;QACEiB,IAAI,EAAC,QAAQ;QACbiE,WAAW,EAAC,kBAAkB;QAC9B7D,SAAS,EAAC,cAAc;QACxB8D,KAAK,EAAEnC,UAAW;QAClBoC,QAAQ,EAAGC,CAAC,IAAKpC,aAAa,CAACoC,CAAC,CAACZ,MAAM,CAACU,KAAK;MAAE;QAAA7D,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAChD;IAAC;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACC,CAAC,eAGNzB,OAAA;MAAKqB,SAAS,EAAC,eAAe;MAAAO,QAAA,EAC3ByB,UAAU,CAACiC,GAAG,CAAElF,QAAQ,iBACvBJ,OAAA;QAEEqB,SAAS,EAAE,gBACTyB,cAAc,KAAK1C,QAAQ,IAAKA,QAAQ,KAAK,KAAK,IAAI,CAAC0C,cAAe,GAClE,QAAQ,GACR,EAAE,EACL;QACHnB,OAAO,EAAEA,CAAA,KAAMoB,iBAAiB,CAAC3C,QAAQ,KAAK,KAAK,GAAG,EAAE,GAAGA,QAAQ,CAAE;QAAAwB,QAAA,EAEpExB,QAAQ,KAAK,KAAK,GAAG,QAAQ,GAAG,GAAGE,eAAe,CAACF,QAAQ,CAAC,IAAIA,QAAQ;MAAE,GARtEA,QAAQ;QAAAkB,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OASP,CACT;IAAC;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACC,CAAC,eAGNzB,OAAA;MAAKqB,SAAS,EAAC,aAAa;MAAAO,QAAA,EACzB+B,cAAc,CAAC4B,MAAM,GAAG,CAAC,GACxB5B,cAAc,CAAC2B,GAAG,CAAE1E,KAAK,iBACvBZ,OAAA;QAEE0B,GAAG,EAAEuC,aAAc;QACnB,iBAAerD,KAAK,CAAC4D,OAAO,IAAI5D,KAAK,CAAC4E,GAAG,IAAI,SAASC,IAAI,CAACC,MAAM,CAAC,CAAC,EAAG;QAAA9D,QAAA,eAEtE5B,OAAA,CAACQ,YAAY;UACXI,KAAK,EAAEA,KAAM;UACbC,UAAU,EAAEA,UAAW;UACvBC,SAAS,EAAEkE,cAAc,CAACpE,KAAK,CAAC4D,OAAO,IAAI5D,KAAK,CAAC4E,GAAG,IAAI,EAAE;QAAE;UAAAlE,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAC7D;MAAC,GARGb,KAAK,CAAC4D,OAAO,IAAI5D,KAAK,CAAC4E,GAAG,IAAI,SAASC,IAAI,CAACC,MAAM,CAAC,CAAC,EAAE;QAAApE,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OASxD,CACN,CAAC,gBAEFzB,OAAA;QAAKqB,SAAS,EAAC,WAAW;QAAAO,QAAA,eACxB5B,OAAA;UAAKqB,SAAS,EAAC,aAAa;UAAAO,QAAA,gBAC1B5B,OAAA;YAAKqB,SAAS,EAAC,eAAe;YAAAO,QAAA,EAAC;UAAE;YAAAN,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAK,CAAC,eACvCzB,OAAA;YAAA4B,QAAA,EAAK;UAAgB;YAAAN,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAK,CAAC,eAC3BzB,OAAA;YAAKqB,SAAS,EAAC,cAAc;YAAAO,QAAA,EAAC;UAAmC;YAAAN,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAK,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACpE;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACH;IACN;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACE,CAAC;EAAA;IAAAH,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OACH,CAAC;AAEV,CAAC;AAACoB,GAAA,CA/HIF,YAAyC;AAAAgD,GAAA,GAAzChD,YAAyC;AAiI/C,eAAAiD,GAAA,gBAAelG,KAAK,CAACgB,IAAI,CAACiC,YAAY,CAAC;AACvC,SAASA,YAAY;AAAG,IAAAhC,EAAA,EAAA+B,GAAA,EAAAiD,GAAA,EAAAC,GAAA;AAAAC,YAAA,CAAAlF,EAAA;AAAAkF,YAAA,CAAAnD,GAAA;AAAAmD,YAAA,CAAAF,GAAA;AAAAE,YAAA,CAAAD,GAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}