/*
 * Enhanced Styles for the Driftly Email Generator Editor
 * Professional design system with glassmorphic elements and smooth animations
 */

/* Base editor layout with Driftly theme */
.email-editor {
  @apply flex flex-col h-screen;
  background: linear-gradient(135deg, var(--color-neutral-base) 0%, var(--color-neutral-light) 100%);
  color: var(--color-text-primary);
  font-family: var(--font-sans);
}

/* Enhanced Toolbar with glassmorphic design */
.editor-toolbar {
  @apply flex items-center justify-between px-6 py-3;
  background: var(--color-glass-bg);
  backdrop-filter: blur(12px);
  -webkit-backdrop-filter: blur(12px);
  border-bottom: 1px solid var(--color-glass-border);
  box-shadow: 0 4px 6px rgba(0, 0, 0, 0.1), 0 1px 3px rgba(0, 0, 0, 0.1);
  position: relative;
  z-index: 10;
}

.toolbar-left {
  @apply flex items-center space-x-4;
}

.toolbar-center {
  @apply flex items-center space-x-2;
}

.toolbar-right {
  @apply flex items-center space-x-3;
}

/* Enhanced toggle buttons with Driftly colors */
.preview-mode-toggle {
  @apply flex items-center rounded-lg p-1;
  background: rgba(31, 41, 55, 0.6);
  border: 1px solid var(--color-glass-border);
  backdrop-filter: blur(8px);
}

.toggle-button {
  @apply flex items-center px-4 py-2 rounded-md text-sm font-medium transition-all duration-200 ease-in-out;
  position: relative;
  overflow: hidden;
}

.toggle-button.active {
  background: var(--color-primary-blue);
  color: var(--color-text-primary);
  box-shadow: 0 2px 4px rgba(30, 93, 166, 0.3);
  transform: translateY(-1px);
}

.toggle-button:not(.active) {
  color: var(--color-text-secondary);
}

.toggle-button:not(.active):hover {
  color: var(--color-text-primary);
  background: rgba(79, 148, 221, 0.1);
  transform: translateY(-1px);
}

.toggle-button .icon {
  @apply mr-2;
  transition: transform 0.2s ease-in-out;
}

.toggle-button:hover .icon {
  transform: scale(1.1);
}

/* Enhanced save button with Driftly styling */
.save-button {
  @apply px-6 py-2 rounded-md text-sm font-semibold transition-all duration-200 ease-in-out;
  background: var(--color-accent-coral);
  color: var(--color-text-primary);
  border: 1px solid transparent;
  box-shadow: 0 2px 4px rgba(79, 148, 221, 0.3);
  position: relative;
  overflow: hidden;
}

.save-button:hover {
  background: var(--color-accent-coral-hover);
  transform: translateY(-1px);
  box-shadow: 0 4px 8px rgba(79, 148, 221, 0.4);
}

.save-button:focus {
  outline: none;
  box-shadow: 0 0 0 2px var(--color-primary-blue-light), 0 0 0 4px rgba(59, 130, 246, 0.3);
}

.save-button:disabled {
  background: var(--color-text-secondary);
  cursor: not-allowed;
  transform: none;
  box-shadow: none;
}

/* Ripple effect for buttons */
.save-button::before,
.toggle-button::before {
  content: '';
  position: absolute;
  top: 50%;
  left: 50%;
  width: 0;
  height: 0;
  border-radius: 50%;
  background: rgba(255, 255, 255, 0.3);
  transform: translate(-50%, -50%);
  transition: width 0.3s, height 0.3s;
}

.save-button:active::before,
.toggle-button:active::before {
  width: 300px;
  height: 300px;
}

/* Enhanced editor content layout */
.editor-content {
  @apply flex flex-1 overflow-hidden;
  gap: 1px;
  background: var(--color-border);
}

/* Enhanced Block library panel with glassmorphic design */
.block-library-panel {
  @apply w-72 flex flex-col overflow-hidden;
  background: var(--color-glass-bg);
  backdrop-filter: blur(12px);
  -webkit-backdrop-filter: blur(12px);
  border-right: 1px solid var(--color-glass-border);
  box-shadow: 2px 0 8px rgba(0, 0, 0, 0.1);
}

.block-library-panel h3 {
  @apply px-6 py-4 text-lg font-semibold;
  color: var(--color-text-primary);
  border-bottom: 1px solid var(--color-glass-border);
  background: rgba(30, 93, 166, 0.1);
  backdrop-filter: blur(8px);
}

.block-library {
  @apply flex flex-col h-full overflow-hidden;
}

/* Enhanced search container */
.search-container {
  @apply p-4;
  border-bottom: 1px solid var(--color-glass-border);
  background: rgba(31, 41, 55, 0.3);
}

.search-input {
  @apply w-full px-4 py-3 rounded-lg text-sm font-medium transition-all duration-200;
  background: rgba(31, 41, 55, 0.6);
  border: 1px solid var(--color-glass-border);
  color: var(--color-text-primary);
  backdrop-filter: blur(8px);
}

.search-input:focus {
  outline: none;
  border-color: var(--color-primary-blue);
  box-shadow: 0 0 0 2px rgba(30, 93, 166, 0.3);
  background: rgba(31, 41, 55, 0.8);
}

.search-input::placeholder {
  color: var(--color-text-secondary);
}

/* Enhanced category tabs */
.category-tabs {
  @apply flex overflow-x-auto p-3 space-x-2;
  border-bottom: 1px solid var(--color-glass-border);
  background: rgba(31, 41, 55, 0.2);
  scrollbar-width: none;
  -ms-overflow-style: none;
}

.category-tabs::-webkit-scrollbar {
  display: none;
}

.category-tab {
  @apply px-4 py-2 text-sm font-medium rounded-full whitespace-nowrap transition-all duration-200;
  border: 1px solid transparent;
  backdrop-filter: blur(8px);
}

.category-tab.active {
  background: var(--color-primary-blue);
  color: var(--color-text-primary);
  border-color: var(--color-primary-blue);
  box-shadow: 0 2px 4px rgba(30, 93, 166, 0.3);
  transform: translateY(-1px);
}

.category-tab:not(.active) {
  color: var(--color-text-secondary);
  background: rgba(31, 41, 55, 0.4);
  border-color: var(--color-glass-border);
}

.category-tab:not(.active):hover {
  color: var(--color-text-primary);
  background: rgba(79, 148, 221, 0.2);
  border-color: var(--color-accent-coral);
  transform: translateY(-1px);
}

/* Enhanced blocks grid */
.blocks-grid {
  @apply grid grid-cols-1 gap-4 p-4 overflow-y-auto;
  scrollbar-width: thin;
  scrollbar-color: var(--color-accent-coral) transparent;
}

.blocks-grid::-webkit-scrollbar {
  width: 6px;
}

.blocks-grid::-webkit-scrollbar-track {
  background: transparent;
}

.blocks-grid::-webkit-scrollbar-thumb {
  background: var(--color-accent-coral);
  border-radius: 3px;
}

.blocks-grid::-webkit-scrollbar-thumb:hover {
  background: var(--color-accent-coral-hover);
}

/* Enhanced library block with modern design */
.library-block {
  @apply flex flex-col rounded-lg overflow-hidden cursor-grab transition-all duration-300;
  background: rgba(31, 41, 55, 0.6);
  border: 1px solid var(--color-glass-border);
  backdrop-filter: blur(8px);
  position: relative;
}

.library-block:hover {
  border-color: var(--color-accent-coral);
  box-shadow: 0 4px 12px rgba(79, 148, 221, 0.2);
  transform: translateY(-2px) scale(1.02);
}

.library-block.dragging {
  border-color: var(--color-primary-blue);
  box-shadow: 0 8px 24px rgba(30, 93, 166, 0.4);
  transform: rotate(2deg) scale(1.05);
  z-index: 1000;
}

/* Enhanced block preview */
.block-preview {
  @apply h-20 flex items-center justify-center overflow-hidden relative;
  background: linear-gradient(135deg, rgba(30, 93, 166, 0.1) 0%, rgba(79, 148, 221, 0.1) 100%);
}

.block-preview img {
  @apply w-full h-full object-cover transition-transform duration-300;
}

.library-block:hover .block-preview img {
  transform: scale(1.1);
}

.block-type-icon {
  @apply w-10 h-10 rounded-full flex items-center justify-center text-lg font-bold transition-all duration-300;
  background: var(--color-accent-coral);
  color: var(--color-text-primary);
  box-shadow: 0 2px 8px rgba(79, 148, 221, 0.3);
}

.library-block:hover .block-type-icon {
  transform: scale(1.1) rotate(5deg);
  box-shadow: 0 4px 12px rgba(79, 148, 221, 0.4);
}

/* Enhanced block info */
.block-info {
  @apply p-4;
}

.block-name {
  @apply text-sm font-semibold mb-2;
  color: var(--color-text-primary);
}

.block-description {
  @apply text-xs leading-relaxed;
  color: var(--color-text-secondary);
}

.no-blocks {
  @apply p-6 text-center;
  color: var(--color-text-secondary);
}

/* Enhanced Editor workspace with modern design */
.editor-workspace {
  @apply flex-1 flex flex-col overflow-hidden;
  background: var(--color-glass-bg);
  backdrop-filter: blur(8px);
  -webkit-backdrop-filter: blur(8px);
}

.blocks-container {
  @apply flex-1 p-6 overflow-y-auto;
  background: linear-gradient(135deg, rgba(31, 41, 55, 0.3) 0%, rgba(17, 24, 39, 0.3) 100%);
  scrollbar-width: thin;
  scrollbar-color: var(--color-accent-coral) transparent;
}

.blocks-container::-webkit-scrollbar {
  width: 8px;
}

.blocks-container::-webkit-scrollbar-track {
  background: transparent;
}

.blocks-container::-webkit-scrollbar-thumb {
  background: var(--color-accent-coral);
  border-radius: 4px;
}

.blocks-container::-webkit-scrollbar-thumb:hover {
  background: var(--color-accent-coral-hover);
}

/* Enhanced empty state */
.empty-blocks {
  @apply flex flex-col items-center justify-center h-full text-center p-12;
  color: var(--color-text-secondary);
  background: rgba(31, 41, 55, 0.2);
  border: 2px dashed var(--color-glass-border);
  border-radius: 16px;
  backdrop-filter: blur(8px);
  transition: all 0.3s ease-in-out;
}

.empty-blocks:hover {
  border-color: var(--color-accent-coral);
  background: rgba(79, 148, 221, 0.1);
  transform: scale(1.02);
}

.empty-blocks::before {
  content: '🎨';
  font-size: 3rem;
  margin-bottom: 1rem;
  opacity: 0.7;
}

/* Enhanced draggable blocks with glassmorphic design */
.draggable-block {
  @apply rounded-lg mb-6 cursor-grab transition-all duration-300;
  background: var(--color-glass-bg);
  border: 1px solid var(--color-glass-border);
  backdrop-filter: blur(12px);
  -webkit-backdrop-filter: blur(12px);
  box-shadow: 0 4px 6px rgba(0, 0, 0, 0.1);
  position: relative;
  overflow: hidden;
}

.draggable-block::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  height: 3px;
  background: linear-gradient(90deg, var(--color-primary-blue) 0%, var(--color-accent-coral) 100%);
  opacity: 0;
  transition: opacity 0.3s ease-in-out;
}

.draggable-block:hover {
  border-color: var(--color-accent-coral);
  box-shadow: 0 8px 16px rgba(79, 148, 221, 0.2);
  transform: translateY(-2px);
}

.draggable-block:hover::before {
  opacity: 1;
}

.draggable-block.dragging {
  border-color: var(--color-primary-blue);
  box-shadow: 0 12px 24px rgba(30, 93, 166, 0.3);
  transform: rotate(1deg) scale(1.02);
  z-index: 1000;
}

.draggable-block.selected {
  border-color: var(--color-accent-coral);
  box-shadow: 0 0 0 2px rgba(79, 148, 221, 0.3), 0 8px 16px rgba(79, 148, 221, 0.2);
}

.draggable-block.selected::before {
  opacity: 1;
}

/* Enhanced block header */
.block-header {
  @apply flex items-center justify-between px-5 py-3;
  background: rgba(30, 93, 166, 0.1);
  border-bottom: 1px solid var(--color-glass-border);
  backdrop-filter: blur(8px);
}

.block-type {
  @apply text-sm font-semibold;
  color: var(--color-text-primary);
}

.block-actions {
  @apply flex items-center space-x-2;
}

/* Enhanced action buttons */
.remove-block,
.duplicate-block {
  @apply p-2 rounded-md transition-all duration-200;
  color: var(--color-text-secondary);
  background: rgba(31, 41, 55, 0.3);
  border: 1px solid transparent;
}

.remove-block:hover {
  color: var(--color-danger);
  background: rgba(220, 38, 38, 0.1);
  border-color: var(--color-danger);
  transform: scale(1.1);
}

.duplicate-block:hover {
  color: var(--color-accent-coral);
  background: rgba(79, 148, 221, 0.1);
  border-color: var(--color-accent-coral);
  transform: scale(1.1);
}

/* Enhanced block content preview */
.block-content-preview {
  @apply p-5 text-sm min-h-[60px];
  color: var(--color-text-secondary);
  background: rgba(31, 41, 55, 0.2);
  backdrop-filter: blur(4px);
}

/* Enhanced Preview panel with glassmorphic design */
.preview-panel {
  @apply w-full md:w-[400px] lg:w-[500px] flex flex-col overflow-hidden;
  background: var(--color-glass-bg);
  backdrop-filter: blur(12px);
  -webkit-backdrop-filter: blur(12px);
  border-left: 1px solid var(--color-glass-border);
  box-shadow: -2px 0 8px rgba(0, 0, 0, 0.1);
}

.preview-panel h3 {
  @apply px-6 py-4 text-lg font-semibold;
  color: var(--color-text-primary);
  border-bottom: 1px solid var(--color-glass-border);
  background: rgba(30, 93, 166, 0.1);
  backdrop-filter: blur(8px);
}

.email-preview {
  @apply flex-1 overflow-hidden p-4;
}

.preview-container {
  @apply flex items-center justify-center h-full;
  background: rgba(31, 41, 55, 0.2);
  border-radius: 12px;
  backdrop-filter: blur(8px);
}

.preview-container.mobile {
  @apply max-w-sm mx-auto;
}

.preview-iframe {
  @apply rounded-lg;
  background: white;
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.15);
}

.empty-template {
  @apply flex flex-col items-center justify-center h-full text-center p-8;
  color: var(--color-text-secondary);
}

.empty-template::before {
  content: '📧';
  font-size: 3rem;
  margin-bottom: 1rem;
  opacity: 0.7;
}

/* Enhanced Block editor panel */
.block-editor-panel {
  @apply w-full md:w-96 flex flex-col overflow-hidden;
  background: var(--color-glass-bg);
  backdrop-filter: blur(12px);
  -webkit-backdrop-filter: blur(12px);
  border-left: 1px solid var(--color-glass-border);
  box-shadow: -2px 0 8px rgba(0, 0, 0, 0.1);
}

.block-editor-panel h3 {
  @apply px-6 py-4 text-lg font-semibold;
  color: var(--color-text-primary);
  border-bottom: 1px solid var(--color-glass-border);
  background: rgba(30, 93, 166, 0.1);
  backdrop-filter: blur(8px);
}

.block-editor {
  @apply p-6 overflow-y-auto;
  scrollbar-width: thin;
  scrollbar-color: var(--color-accent-coral) transparent;
}

.block-editor::-webkit-scrollbar {
  width: 6px;
}

.block-editor::-webkit-scrollbar-track {
  background: transparent;
}

.block-editor::-webkit-scrollbar-thumb {
  background: var(--color-accent-coral);
  border-radius: 3px;
}

.block-editor h4 {
  @apply text-base font-semibold mb-4;
  color: var(--color-text-primary);
}

/* Enhanced form elements */
.form-group {
  @apply mb-6;
}

.form-group label {
  @apply block text-sm font-semibold mb-2;
  color: var(--color-text-primary);
}

.form-control {
  @apply block w-full px-4 py-3 rounded-lg text-sm font-medium transition-all duration-200;
  background: rgba(31, 41, 55, 0.6);
  border: 1px solid var(--color-glass-border);
  color: var(--color-text-primary);
  backdrop-filter: blur(8px);
}

.form-control:focus {
  outline: none;
  border-color: var(--color-primary-blue);
  box-shadow: 0 0 0 2px rgba(30, 93, 166, 0.3);
  background: rgba(31, 41, 55, 0.8);
}

.form-control::placeholder {
  color: var(--color-text-secondary);
}

.input-with-suggestion {
  @apply relative;
}

.suggestion-button {
  @apply absolute right-3 top-1/2 transform -translate-y-1/2 p-2 rounded-md transition-all duration-200;
  color: var(--color-text-secondary);
  background: rgba(31, 41, 55, 0.3);
}

.suggestion-button:hover {
  color: var(--color-accent-coral);
  background: rgba(79, 148, 221, 0.2);
  transform: translateY(-50%) scale(1.1);
}

/* Enhanced AI Suggestion Panel */
.ai-suggestion-panel {
  @apply mt-6 rounded-lg overflow-hidden;
  background: var(--color-glass-bg);
  border: 1px solid var(--color-glass-border);
  backdrop-filter: blur(8px);
}

.suggestion-header {
  @apply flex items-center justify-between px-5 py-3;
  background: rgba(79, 148, 221, 0.1);
  border-bottom: 1px solid var(--color-glass-border);
}

.suggestion-header h4 {
  @apply text-sm font-semibold m-0;
  color: var(--color-accent-coral);
}

.close-button {
  @apply p-1 rounded-md transition-all duration-200;
  color: var(--color-text-secondary);
  background: rgba(31, 41, 55, 0.3);
}

.close-button:hover {
  color: var(--color-text-primary);
  background: rgba(79, 148, 221, 0.2);
  transform: scale(1.1);
}

.suggestion-content {
  @apply p-4;
  background: rgba(31, 41, 55, 0.2);
}

.loading-suggestions {
  @apply flex flex-col items-center justify-center py-6;
  color: var(--color-text-secondary);
}

.spinner {
  @apply w-8 h-8 border-2 rounded-full animate-spin mb-3;
  border-color: var(--color-accent-coral);
  border-top-color: transparent;
}

.no-suggestions {
  @apply py-4 text-center;
  color: var(--color-text-secondary);
}

.suggestions-list {
  @apply space-y-3;
}

.suggestion-item {
  @apply p-3 rounded-lg flex items-center justify-between transition-all duration-200;
  background: rgba(31, 41, 55, 0.4);
  border: 1px solid var(--color-glass-border);
}

.suggestion-item:hover {
  background: rgba(79, 148, 221, 0.1);
  border-color: var(--color-accent-coral);
  transform: translateY(-1px);
}

.suggestion-text {
  @apply text-sm pr-3;
  color: var(--color-text-primary);
}

.apply-button {
  @apply px-3 py-1 text-xs font-semibold rounded-md transition-all duration-200;
  background: var(--color-accent-coral);
  color: var(--color-text-primary);
}

.apply-button:hover {
  background: var(--color-accent-coral-hover);
  transform: scale(1.05);
}

/* Enhanced Modal with glassmorphic design */
.modal-overlay {
  @apply fixed inset-0 flex items-center justify-center z-50;
  background: rgba(0, 0, 0, 0.7);
  backdrop-filter: blur(8px);
  -webkit-backdrop-filter: blur(8px);
}

.modal-container {
  @apply w-full max-w-md mx-4 rounded-xl overflow-hidden;
  background: var(--color-glass-bg);
  border: 1px solid var(--color-glass-border);
  backdrop-filter: blur(16px);
  -webkit-backdrop-filter: blur(16px);
  box-shadow: 0 20px 40px rgba(0, 0, 0, 0.3);
}

.modal-header {
  @apply flex items-center justify-between px-6 py-4;
  background: rgba(30, 93, 166, 0.1);
  border-bottom: 1px solid var(--color-glass-border);
}

.modal-header h3 {
  @apply text-lg font-semibold;
  color: var(--color-text-primary);
}

.modal-body {
  @apply px-6 py-6;
}

.error-message {
  @apply mb-4 p-4 rounded-lg text-sm;
  background: rgba(220, 38, 38, 0.1);
  color: var(--color-danger);
  border: 1px solid rgba(220, 38, 38, 0.3);
}

.form-checkbox {
  @apply h-4 w-4 rounded;
  accent-color: var(--color-accent-coral);
}

.form-group.checkbox {
  @apply flex items-center;
}

.form-group.checkbox label {
  @apply ml-3 block text-sm;
  color: var(--color-text-primary);
}

.modal-footer {
  @apply px-6 py-4 flex justify-end space-x-3;
  background: rgba(31, 41, 55, 0.3);
  border-top: 1px solid var(--color-glass-border);
}

.cancel-button {
  @apply px-4 py-2 rounded-md text-sm font-medium transition-all duration-200;
  background: rgba(31, 41, 55, 0.6);
  border: 1px solid var(--color-glass-border);
  color: var(--color-text-primary);
}

.cancel-button:hover {
  background: rgba(79, 148, 221, 0.2);
  border-color: var(--color-accent-coral);
  transform: translateY(-1px);
}

.cancel-button:disabled {
  background: var(--color-text-secondary);
  cursor: not-allowed;
  transform: none;
}

/* Enhanced drag and drop visual feedback */
.drop-zone-active {
  @apply relative;
}

.drop-zone-active::after {
  content: '';
  position: absolute;
  inset: 0;
  background: rgba(79, 148, 221, 0.1);
  border: 2px dashed var(--color-accent-coral);
  border-radius: 12px;
  backdrop-filter: blur(4px);
  z-index: 10;
}

.drag-ghost {
  @apply opacity-50 transform rotate-2 scale-105;
  filter: blur(1px);
}

/* Loading states */
.loading-overlay {
  @apply absolute inset-0 flex items-center justify-center;
  background: var(--color-glass-bg);
  backdrop-filter: blur(8px);
  z-index: 20;
}

.loading-spinner {
  @apply w-12 h-12 border-4 rounded-full animate-spin;
  border-color: var(--color-accent-coral);
  border-top-color: transparent;
}

/* Enhanced responsive design */
@media (max-width: 1024px) {
  .block-library-panel {
    @apply w-64;
  }
  .preview-panel,
  .block-editor-panel {
    @apply w-80;
  }
}

@media (max-width: 768px) {
  .email-editor {
    @apply h-screen;
  }

  .editor-toolbar {
    @apply px-4 py-2 flex-wrap;
  }

  .toolbar-center {
    @apply order-3 w-full mt-2;
  }

  .block-library-panel {
    @apply w-full h-64 border-r-0 border-b;
  }

  .editor-content {
    @apply flex-col;
  }

  .editor-workspace {
    @apply order-2;
  }

  .preview-panel,
  .block-editor-panel {
    @apply w-full border-l-0 border-t max-h-80;
  }

  .blocks-container {
    @apply p-4;
  }

  .draggable-block {
    @apply mb-4;
  }
}

@media (max-width: 480px) {
  .toolbar-left,
  .toolbar-right {
    @apply flex-wrap;
  }

  .toggle-button {
    @apply px-2 py-1 text-xs;
  }

  .save-button {
    @apply px-4 py-2 text-xs;
  }

  .block-library-panel h3,
  .preview-panel h3,
  .block-editor-panel h3 {
    @apply px-4 py-3 text-base;
  }
}
