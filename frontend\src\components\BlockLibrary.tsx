/**
 * Enhanced BlockLibrary component for Driftly Email Generator
 * Displays available blocks with improved UI, categorization, and drag feedback
 */

import React, {
  useCallback,
  useMemo,
  useState,
} from 'react';

import { useDrag } from 'react-dnd';

// Import Block interface from types to ensure consistency
import { Block } from '../types/editor';

// Define ItemTypes if not imported elsewhere
const ItemTypes = {
  LIBRARY_BLOCK: 'library_block'
};

interface BlockLibraryProps {
  blocks: Block[];
  onAddBlock: (block: Block) => void;
}

// Helper function to get category colors with Driftly theme
const getCategoryColor = (category: string): string => {
  const colors: Record<string, string> = {
    'Header': 'bg-gradient-to-r from-purple-500 to-purple-600 text-white',
    'Content': 'bg-gradient-to-r from-blue-500 to-blue-600 text-white',
    'Layout': 'bg-gradient-to-r from-green-500 to-green-600 text-white',
    'Footer': 'bg-gradient-to-r from-orange-500 to-orange-600 text-white',
    'CTA': 'bg-gradient-to-r from-red-500 to-red-600 text-white',
    'Social': 'bg-gradient-to-r from-pink-500 to-pink-600 text-white',
    'Product': 'bg-gradient-to-r from-yellow-500 to-yellow-600 text-white',
    'Navigation': 'bg-gradient-to-r from-indigo-500 to-indigo-600 text-white',
  };
  return colors[category] || 'bg-gradient-to-r from-gray-500 to-gray-600 text-white';
};

// Helper function to get category icons
const getCategoryIcon = (category: string): string => {
  const icons: Record<string, string> = {
    'Header': '🏠',
    'Content': '📝',
    'Layout': '📐',
    'Footer': '🦶',
    'CTA': '🎯',
    'Social': '📱',
    'Product': '🛍️',
    'Navigation': '🧭',
  };
  return icons[category] || '📦';
};

// Component that renders each individual library block
const LibraryBlock: React.FC<{
  block: Block,
  onAddBlock: (block: Block) => void,
  isVisible: boolean
}> = React.memo(({ block, onAddBlock, isVisible }) => {
  const [{ isDragging }, drag] = useDrag(() => ({
    type: ItemTypes.LIBRARY_BLOCK,
    item: { block, type: ItemTypes.LIBRARY_BLOCK },
    collect: (monitor) => ({
      isDragging: monitor.isDragging()
    })
  }), [block]);

  // Don't render content if block is not visible
  if (!isVisible) {
    return <div className="h-20 bg-gray-100 animate-pulse rounded" />; // Loading placeholder
  }

  return (
    <div
      ref={drag}
      className={`library-block ${isDragging ? 'dragging' : ''}`}
      onClick={() => onAddBlock(block)}
    >
      <div className="flex items-center justify-between mb-3">
        <h5 className="block-name truncate">{block.name}</h5>
        <span className={`text-xs px-2 py-1 rounded-full font-semibold shadow-sm ${
          getCategoryColor(block.category)
        }`}>
          {getCategoryIcon(block.category)} {block.category}
        </span>
      </div>

      <div className="block-preview">
        {block.thumbnail ? (
          <img
            src={block.thumbnail}
            alt={block.name}
            className="w-full h-full object-cover"
            loading="lazy"
          />
        ) : (
          <div className="text-center">
            <div className="block-type-icon">
              {getCategoryIcon(block.category)}
            </div>
          </div>
        )}
      </div>

      <div className="block-info">
        {block.description && (
          <div className="block-description line-clamp-2">
            {block.description}
          </div>
        )}
      </div>

      {/* Enhanced drag indicator */}
      {isDragging && (
        <div className="absolute inset-0 bg-gradient-to-br from-primary-blue/20 to-accent-coral/20 rounded-lg flex items-center justify-center backdrop-blur-sm">
          <div className="text-accent-coral font-semibold text-sm flex items-center">
            <svg className="w-4 h-4 mr-2 animate-pulse" fill="none" stroke="currentColor" viewBox="0 0 24 24">
              <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M7 16V4m0 0L3 8m4-4l4 4m6 0v12m0 0l4-4m-4 4l-4-4" />
            </svg>
            Dragging...
          </div>
        </div>
      )}
    </div>
  );
});

// Main component for block library
const BlockLibrary: React.FC<BlockLibraryProps> = ({ blocks, onAddBlock }) => {
  const [activeCategory, setActiveCategory] = useState<string>('');
  const [searchTerm, setSearchTerm] = useState<string>('');
  const [visibleBlocks, setVisibleBlocks] = useState<Set<string>>(new Set());

  // Get unique categories for filter tabs
  const categories = useMemo(() => {
    const cats = new Set<string>();
    blocks.forEach(block => {
      if (block.category) cats.add(block.category);
    });
    return ['All', ...Array.from(cats)];
  }, [blocks]);

  // Filter blocks based on category and search term
  const filteredBlocks = useMemo(() => {
    return blocks.filter(block => {
      // Category filter
      if (activeCategory && activeCategory !== 'All' && block.category !== activeCategory) {
        return false;
      }

      // Search filter
      if (searchTerm.trim() !== '') {
        const searchLower = searchTerm.toLowerCase();
        return (
          block.name.toLowerCase().includes(searchLower) ||
          (block.description || '').toLowerCase().includes(searchLower) ||
          (block.category || '').toLowerCase().includes(searchLower)
        );
      }

      return true;
    });
  }, [blocks, activeCategory, searchTerm]);

  // Implement intersection observer for lazy loading
  const blockObserver = useCallback((node: HTMLDivElement | null) => {
    if (!node) return;

    const observer = new IntersectionObserver(
      (entries) => {
        entries.forEach(entry => {
          if (entry.isIntersecting) {
            const blockId = entry.target.getAttribute('data-block-id');
            if (blockId) {
              // Fix Set iteration by using current value to create a new Set
              setVisibleBlocks(prev => {
                const newSet = new Set(prev);
                newSet.add(blockId);
                return newSet;
              });
            }
          }
        });
      },
      { rootMargin: '200px 0px' } // Load blocks that are 200px outside viewport
    );

    observer.observe(node);

    return () => observer.disconnect();
  }, []);

  // Cache check for block visibility
  const isBlockVisible = useCallback((blockId: string) => {
    return visibleBlocks.has(blockId);
  }, [visibleBlocks]);

  return (
    <div className="block-library">
      {/* Enhanced Search Box */}
      <div className="search-container">
        <input
          type="search"
          placeholder="Search blocks..."
          className="search-input"
          value={searchTerm}
          onChange={(e) => setSearchTerm(e.target.value)}
        />
      </div>

      {/* Enhanced Category Tabs */}
      <div className="category-tabs">
        {categories.map((category) => (
          <button
            key={category}
            className={`category-tab ${
              activeCategory === category || (category === 'All' && !activeCategory)
                ? 'active'
                : ''
            }`}
            onClick={() => setActiveCategory(category === 'All' ? '' : category)}
          >
            {category === 'All' ? '🎨 All' : `${getCategoryIcon(category)} ${category}`}
          </button>
        ))}
      </div>

      {/* Enhanced Block List */}
      <div className="blocks-grid">
        {filteredBlocks.length > 0 ? (
          filteredBlocks.map((block) => (
            <div
              key={block.blockId || block._id || `block-${Math.random()}`}
              ref={blockObserver}
              data-block-id={block.blockId || block._id || `block-${Math.random()}`}
            >
              <LibraryBlock
                block={block}
                onAddBlock={onAddBlock}
                isVisible={isBlockVisible(block.blockId || block._id || '')}
              />
            </div>
          ))
        ) : (
          <div className="no-blocks">
            <div className="text-center">
              <div className="text-4xl mb-2">🔍</div>
              <div>No blocks found.</div>
              <div className="text-xs mt-1">Try a different search or category.</div>
            </div>
          </div>
        )}
      </div>
    </div>
  );
};

export default React.memo(BlockLibrary);
export { BlockLibrary };
