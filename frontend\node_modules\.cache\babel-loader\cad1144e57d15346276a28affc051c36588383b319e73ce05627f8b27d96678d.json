{"ast": null, "code": "var _jsxFileName = \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\DONT DELETE\\\\driftly-enhanced-current-2.0.0\\\\frontend\\\\src\\\\components\\\\BlockLibrary.tsx\",\n  _s = $RefreshSig$(),\n  _s2 = $RefreshSig$();\n/**\n * Enhanced BlockLibrary component for Driftly Email Generator\n * Displays available blocks with improved UI, categorization, and drag feedback\n */\n\nimport React, { useCallback, useMemo, useState } from 'react';\nimport { useDrag } from 'react-dnd';\n\n// Import Block interface from types to ensure consistency\nimport { jsxDEV as _jsxDEV } from \"react/jsx-dev-runtime\";\n// Define ItemTypes if not imported elsewhere\nconst ItemTypes = {\n  LIBRARY_BLOCK: 'library_block'\n};\n// Helper function to get category colors with Driftly theme\nconst getCategoryColor = category => {\n  const colors = {\n    'Header': 'bg-gradient-to-r from-purple-500 to-purple-600 text-white',\n    'Content': 'bg-gradient-to-r from-blue-500 to-blue-600 text-white',\n    'Layout': 'bg-gradient-to-r from-green-500 to-green-600 text-white',\n    'Footer': 'bg-gradient-to-r from-orange-500 to-orange-600 text-white',\n    'CTA': 'bg-gradient-to-r from-red-500 to-red-600 text-white',\n    'Social': 'bg-gradient-to-r from-pink-500 to-pink-600 text-white',\n    'Product': 'bg-gradient-to-r from-yellow-500 to-yellow-600 text-white',\n    'Navigation': 'bg-gradient-to-r from-indigo-500 to-indigo-600 text-white'\n  };\n  return colors[category] || 'bg-gradient-to-r from-gray-500 to-gray-600 text-white';\n};\n\n// Helper function to get category icons\nconst getCategoryIcon = category => {\n  const icons = {\n    'Header': '🏠',\n    'Content': '📝',\n    'Layout': '📐',\n    'Footer': '🦶',\n    'CTA': '🎯',\n    'Social': '📱',\n    'Product': '🛍️',\n    'Navigation': '🧭'\n  };\n  return icons[category] || '📦';\n};\n\n// Component that renders each individual library block\nconst LibraryBlock = /*#__PURE__*/_s(/*#__PURE__*/React.memo(_c = _s(({\n  block,\n  onAddBlock,\n  isVisible\n}) => {\n  _s();\n  const [{\n    isDragging\n  }, drag] = useDrag(() => ({\n    type: ItemTypes.LIBRARY_BLOCK,\n    item: {\n      block,\n      type: ItemTypes.LIBRARY_BLOCK\n    },\n    collect: monitor => ({\n      isDragging: monitor.isDragging()\n    })\n  }), [block]);\n\n  // Don't render content if block is not visible\n  if (!isVisible) {\n    return /*#__PURE__*/_jsxDEV(\"div\", {\n      className: \"h-20 bg-gray-100 animate-pulse rounded\"\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 73,\n      columnNumber: 12\n    }, this); // Loading placeholder\n  }\n  return /*#__PURE__*/_jsxDEV(\"div\", {\n    ref: drag,\n    className: `library-block p-3 mb-2 bg-white border rounded-md cursor-move hover:border-blue-400 hover:shadow-md transition-all transform hover:scale-105 ${isDragging ? 'opacity-50 border-blue-500 shadow-lg scale-105' : 'border-gray-200'}`,\n    onClick: () => onAddBlock(block),\n    children: [/*#__PURE__*/_jsxDEV(\"div\", {\n      className: \"flex items-center justify-between mb-2\",\n      children: [/*#__PURE__*/_jsxDEV(\"h5\", {\n        className: \"text-sm font-medium text-gray-800 truncate\",\n        children: block.name\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 85,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n        className: `text-xs px-2 py-0.5 rounded-full font-medium ${getCategoryColor(block.category)}`,\n        children: block.category\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 86,\n        columnNumber: 9\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 84,\n      columnNumber: 7\n    }, this), block.thumbnail ? /*#__PURE__*/_jsxDEV(\"div\", {\n      className: \"mt-2 w-full h-16 flex items-center justify-center overflow-hidden bg-gray-50 rounded border\",\n      children: /*#__PURE__*/_jsxDEV(\"img\", {\n        src: block.thumbnail,\n        alt: block.name,\n        className: \"max-h-full max-w-full object-contain\",\n        loading: \"lazy\"\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 95,\n        columnNumber: 11\n      }, this)\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 94,\n      columnNumber: 9\n    }, this) : /*#__PURE__*/_jsxDEV(\"div\", {\n      className: \"mt-2 h-16 flex items-center justify-center bg-gray-50 rounded border\",\n      children: /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"text-center\",\n        children: [/*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"w-8 h-8 mx-auto mb-1 bg-gray-200 rounded flex items-center justify-center\",\n          children: /*#__PURE__*/_jsxDEV(\"svg\", {\n            className: \"w-4 h-4 text-gray-400\",\n            fill: \"none\",\n            stroke: \"currentColor\",\n            viewBox: \"0 0 24 24\",\n            children: /*#__PURE__*/_jsxDEV(\"path\", {\n              strokeLinecap: \"round\",\n              strokeLinejoin: \"round\",\n              strokeWidth: 2,\n              d: \"M19 11H5m14 0a2 2 0 012 2v6a2 2 0 01-2 2H5a2 2 0 01-2-2v-6a2 2 0 012-2m14 0V9a2 2 0 00-2-2M5 11V9a2 2 0 012-2m0 0V5a2 2 0 012-2h6a2 2 0 012 2v2M7 7h10\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 107,\n              columnNumber: 17\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 106,\n            columnNumber: 15\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 105,\n          columnNumber: 13\n        }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"text-xs text-gray-400\",\n          children: \"No preview\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 110,\n          columnNumber: 13\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 104,\n        columnNumber: 11\n      }, this)\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 103,\n      columnNumber: 9\n    }, this), block.description && /*#__PURE__*/_jsxDEV(\"div\", {\n      className: \"mt-2 text-xs text-gray-500 line-clamp-2\",\n      children: block.description\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 116,\n      columnNumber: 9\n    }, this), isDragging && /*#__PURE__*/_jsxDEV(\"div\", {\n      className: \"absolute inset-0 bg-blue-500 bg-opacity-10 rounded-md flex items-center justify-center\",\n      children: /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"text-blue-600 font-medium text-sm\",\n        children: \"Dragging...\"\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 124,\n        columnNumber: 11\n      }, this)\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 123,\n      columnNumber: 9\n    }, this)]\n  }, void 0, true, {\n    fileName: _jsxFileName,\n    lineNumber: 77,\n    columnNumber: 5\n  }, this);\n}, \"wuumsFp4qAni9XRJfRhQAZjuD/k=\", false, function () {\n  return [useDrag];\n})), \"wuumsFp4qAni9XRJfRhQAZjuD/k=\", false, function () {\n  return [useDrag];\n});\n\n// Main component for block library\n_c2 = LibraryBlock;\nconst BlockLibrary = ({\n  blocks,\n  onAddBlock\n}) => {\n  _s2();\n  const [activeCategory, setActiveCategory] = useState('');\n  const [searchTerm, setSearchTerm] = useState('');\n  const [visibleBlocks, setVisibleBlocks] = useState(new Set());\n\n  // Get unique categories for filter tabs\n  const categories = useMemo(() => {\n    const cats = new Set();\n    blocks.forEach(block => {\n      if (block.category) cats.add(block.category);\n    });\n    return ['All', ...Array.from(cats)];\n  }, [blocks]);\n\n  // Filter blocks based on category and search term\n  const filteredBlocks = useMemo(() => {\n    return blocks.filter(block => {\n      // Category filter\n      if (activeCategory && activeCategory !== 'All' && block.category !== activeCategory) {\n        return false;\n      }\n\n      // Search filter\n      if (searchTerm.trim() !== '') {\n        const searchLower = searchTerm.toLowerCase();\n        return block.name.toLowerCase().includes(searchLower) || (block.description || '').toLowerCase().includes(searchLower) || (block.category || '').toLowerCase().includes(searchLower);\n      }\n      return true;\n    });\n  }, [blocks, activeCategory, searchTerm]);\n\n  // Implement intersection observer for lazy loading\n  const blockObserver = useCallback(node => {\n    if (!node) return;\n    const observer = new IntersectionObserver(entries => {\n      entries.forEach(entry => {\n        if (entry.isIntersecting) {\n          const blockId = entry.target.getAttribute('data-block-id');\n          if (blockId) {\n            // Fix Set iteration by using current value to create a new Set\n            setVisibleBlocks(prev => {\n              const newSet = new Set(prev);\n              newSet.add(blockId);\n              return newSet;\n            });\n          }\n        }\n      });\n    }, {\n      rootMargin: '200px 0px'\n    } // Load blocks that are 200px outside viewport\n    );\n    observer.observe(node);\n    return () => observer.disconnect();\n  }, []);\n\n  // Cache check for block visibility\n  const isBlockVisible = useCallback(blockId => {\n    return visibleBlocks.has(blockId);\n  }, [visibleBlocks]);\n  return /*#__PURE__*/_jsxDEV(\"div\", {\n    className: \"block-library flex flex-col h-full overflow-hidden\",\n    children: [/*#__PURE__*/_jsxDEV(\"div\", {\n      className: \"px-3 py-2 border-b border-gray-200\",\n      children: /*#__PURE__*/_jsxDEV(\"input\", {\n        type: \"search\",\n        placeholder: \"Search blocks...\",\n        className: \"w-full px-3 py-1.5 text-sm border border-gray-300 rounded-md focus:outline-none focus:ring-1 focus:ring-blue-500\",\n        value: searchTerm,\n        onChange: e => setSearchTerm(e.target.value)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 205,\n        columnNumber: 9\n      }, this)\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 204,\n      columnNumber: 7\n    }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n      className: \"flex flex-nowrap overflow-x-auto px-3 py-2 border-b border-gray-200 hide-scrollbar\",\n      children: categories.map(category => /*#__PURE__*/_jsxDEV(\"button\", {\n        className: `px-3 py-1 mr-2 text-xs font-medium rounded-full whitespace-nowrap transition-colors ${activeCategory === category || category === 'All' && !activeCategory ? 'bg-blue-100 text-blue-700' : 'bg-gray-100 text-gray-700 hover:bg-gray-200'}`,\n        onClick: () => setActiveCategory(category === 'All' ? '' : category),\n        children: category\n      }, category, false, {\n        fileName: _jsxFileName,\n        lineNumber: 217,\n        columnNumber: 11\n      }, this))\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 215,\n      columnNumber: 7\n    }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n      className: \"flex-1 overflow-y-auto p-3\",\n      children: filteredBlocks.length > 0 ? filteredBlocks.map(block => /*#__PURE__*/_jsxDEV(\"div\", {\n        ref: blockObserver,\n        \"data-block-id\": block.blockId || block._id || `block-${Math.random()}`,\n        children: /*#__PURE__*/_jsxDEV(LibraryBlock, {\n          block: block,\n          onAddBlock: onAddBlock,\n          isVisible: isBlockVisible(block.blockId || block._id || '')\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 240,\n          columnNumber: 15\n        }, this)\n      }, block.blockId || block._id || `block-${Math.random()}`, false, {\n        fileName: _jsxFileName,\n        lineNumber: 235,\n        columnNumber: 13\n      }, this)) : /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"text-sm text-gray-500 text-center p-4\",\n        children: \"No blocks found. Try a different search or category.\"\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 248,\n        columnNumber: 11\n      }, this)\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 232,\n      columnNumber: 7\n    }, this)]\n  }, void 0, true, {\n    fileName: _jsxFileName,\n    lineNumber: 202,\n    columnNumber: 5\n  }, this);\n};\n_s2(BlockLibrary, \"GomSnj/dpTzNhBMlHfUCUS9rOqQ=\");\n_c3 = BlockLibrary;\nexport default _c4 = /*#__PURE__*/React.memo(BlockLibrary);\nexport { BlockLibrary };\nvar _c, _c2, _c3, _c4;\n$RefreshReg$(_c, \"LibraryBlock$React.memo\");\n$RefreshReg$(_c2, \"LibraryBlock\");\n$RefreshReg$(_c3, \"BlockLibrary\");\n$RefreshReg$(_c4, \"%default%\");", "map": {"version": 3, "names": ["React", "useCallback", "useMemo", "useState", "useDrag", "jsxDEV", "_jsxDEV", "ItemTypes", "LIBRARY_BLOCK", "getCategoryColor", "category", "colors", "getCategoryIcon", "icons", "LibraryBlock", "_s", "memo", "_c", "block", "onAddBlock", "isVisible", "isDragging", "drag", "type", "item", "collect", "monitor", "className", "fileName", "_jsxFileName", "lineNumber", "columnNumber", "ref", "onClick", "children", "name", "thumbnail", "src", "alt", "loading", "fill", "stroke", "viewBox", "strokeLinecap", "strokeLinejoin", "strokeWidth", "d", "description", "_c2", "BlockLibrary", "blocks", "_s2", "activeCategory", "setActiveCategory", "searchTerm", "setSearchTerm", "visibleBlocks", "setVisibleBlocks", "Set", "categories", "cats", "for<PERSON>ach", "add", "Array", "from", "filteredBlocks", "filter", "trim", "searchLower", "toLowerCase", "includes", "blockObserver", "node", "observer", "IntersectionObserver", "entries", "entry", "isIntersecting", "blockId", "target", "getAttribute", "prev", "newSet", "rootMargin", "observe", "disconnect", "isBlockVisible", "has", "placeholder", "value", "onChange", "e", "map", "length", "_id", "Math", "random", "_c3", "_c4", "$RefreshReg$"], "sources": ["C:/Users/<USER>/OneDrive/Desktop/DONT DELETE/driftly-enhanced-current-2.0.0/frontend/src/components/BlockLibrary.tsx"], "sourcesContent": ["/**\n * Enhanced BlockLibrary component for Driftly Email Generator\n * Displays available blocks with improved UI, categorization, and drag feedback\n */\n\nimport React, {\n  useCallback,\n  useMemo,\n  useState,\n} from 'react';\n\nimport { useDrag } from 'react-dnd';\n\n// Import Block interface from types to ensure consistency\nimport { Block } from '../types/editor';\n\n// Define ItemTypes if not imported elsewhere\nconst ItemTypes = {\n  LIBRARY_BLOCK: 'library_block'\n};\n\ninterface BlockLibraryProps {\n  blocks: Block[];\n  onAddBlock: (block: Block) => void;\n}\n\n// Helper function to get category colors with Driftly theme\nconst getCategoryColor = (category: string): string => {\n  const colors: Record<string, string> = {\n    'Header': 'bg-gradient-to-r from-purple-500 to-purple-600 text-white',\n    'Content': 'bg-gradient-to-r from-blue-500 to-blue-600 text-white',\n    'Layout': 'bg-gradient-to-r from-green-500 to-green-600 text-white',\n    'Footer': 'bg-gradient-to-r from-orange-500 to-orange-600 text-white',\n    'CTA': 'bg-gradient-to-r from-red-500 to-red-600 text-white',\n    'Social': 'bg-gradient-to-r from-pink-500 to-pink-600 text-white',\n    'Product': 'bg-gradient-to-r from-yellow-500 to-yellow-600 text-white',\n    'Navigation': 'bg-gradient-to-r from-indigo-500 to-indigo-600 text-white',\n  };\n  return colors[category] || 'bg-gradient-to-r from-gray-500 to-gray-600 text-white';\n};\n\n// Helper function to get category icons\nconst getCategoryIcon = (category: string): string => {\n  const icons: Record<string, string> = {\n    'Header': '🏠',\n    'Content': '📝',\n    'Layout': '📐',\n    'Footer': '🦶',\n    'CTA': '🎯',\n    'Social': '📱',\n    'Product': '🛍️',\n    'Navigation': '🧭',\n  };\n  return icons[category] || '📦';\n};\n\n// Component that renders each individual library block\nconst LibraryBlock: React.FC<{\n  block: Block,\n  onAddBlock: (block: Block) => void,\n  isVisible: boolean\n}> = React.memo(({ block, onAddBlock, isVisible }) => {\n  const [{ isDragging }, drag] = useDrag(() => ({\n    type: ItemTypes.LIBRARY_BLOCK,\n    item: { block, type: ItemTypes.LIBRARY_BLOCK },\n    collect: (monitor) => ({\n      isDragging: monitor.isDragging()\n    })\n  }), [block]);\n\n  // Don't render content if block is not visible\n  if (!isVisible) {\n    return <div className=\"h-20 bg-gray-100 animate-pulse rounded\" />; // Loading placeholder\n  }\n\n  return (\n    <div\n      ref={drag}\n      className={`library-block p-3 mb-2 bg-white border rounded-md cursor-move hover:border-blue-400 hover:shadow-md transition-all transform hover:scale-105 ${\n        isDragging ? 'opacity-50 border-blue-500 shadow-lg scale-105' : 'border-gray-200'\n      }`}\n      onClick={() => onAddBlock(block)}\n    >\n      <div className=\"flex items-center justify-between mb-2\">\n        <h5 className=\"text-sm font-medium text-gray-800 truncate\">{block.name}</h5>\n        <span className={`text-xs px-2 py-0.5 rounded-full font-medium ${\n          getCategoryColor(block.category)\n        }`}>\n          {block.category}\n        </span>\n      </div>\n\n      {block.thumbnail ? (\n        <div className=\"mt-2 w-full h-16 flex items-center justify-center overflow-hidden bg-gray-50 rounded border\">\n          <img\n            src={block.thumbnail}\n            alt={block.name}\n            className=\"max-h-full max-w-full object-contain\"\n            loading=\"lazy\"\n          />\n        </div>\n      ) : (\n        <div className=\"mt-2 h-16 flex items-center justify-center bg-gray-50 rounded border\">\n          <div className=\"text-center\">\n            <div className=\"w-8 h-8 mx-auto mb-1 bg-gray-200 rounded flex items-center justify-center\">\n              <svg className=\"w-4 h-4 text-gray-400\" fill=\"none\" stroke=\"currentColor\" viewBox=\"0 0 24 24\">\n                <path strokeLinecap=\"round\" strokeLinejoin=\"round\" strokeWidth={2} d=\"M19 11H5m14 0a2 2 0 012 2v6a2 2 0 01-2 2H5a2 2 0 01-2-2v-6a2 2 0 012-2m14 0V9a2 2 0 00-2-2M5 11V9a2 2 0 012-2m0 0V5a2 2 0 012-2h6a2 2 0 012 2v2M7 7h10\" />\n              </svg>\n            </div>\n            <div className=\"text-xs text-gray-400\">No preview</div>\n          </div>\n        </div>\n      )}\n\n      {block.description && (\n        <div className=\"mt-2 text-xs text-gray-500 line-clamp-2\">\n          {block.description}\n        </div>\n      )}\n\n      {/* Drag indicator */}\n      {isDragging && (\n        <div className=\"absolute inset-0 bg-blue-500 bg-opacity-10 rounded-md flex items-center justify-center\">\n          <div className=\"text-blue-600 font-medium text-sm\">Dragging...</div>\n        </div>\n      )}\n    </div>\n  );\n});\n\n// Main component for block library\nconst BlockLibrary: React.FC<BlockLibraryProps> = ({ blocks, onAddBlock }) => {\n  const [activeCategory, setActiveCategory] = useState<string>('');\n  const [searchTerm, setSearchTerm] = useState<string>('');\n  const [visibleBlocks, setVisibleBlocks] = useState<Set<string>>(new Set());\n\n  // Get unique categories for filter tabs\n  const categories = useMemo(() => {\n    const cats = new Set<string>();\n    blocks.forEach(block => {\n      if (block.category) cats.add(block.category);\n    });\n    return ['All', ...Array.from(cats)];\n  }, [blocks]);\n\n  // Filter blocks based on category and search term\n  const filteredBlocks = useMemo(() => {\n    return blocks.filter(block => {\n      // Category filter\n      if (activeCategory && activeCategory !== 'All' && block.category !== activeCategory) {\n        return false;\n      }\n\n      // Search filter\n      if (searchTerm.trim() !== '') {\n        const searchLower = searchTerm.toLowerCase();\n        return (\n          block.name.toLowerCase().includes(searchLower) ||\n          (block.description || '').toLowerCase().includes(searchLower) ||\n          (block.category || '').toLowerCase().includes(searchLower)\n        );\n      }\n\n      return true;\n    });\n  }, [blocks, activeCategory, searchTerm]);\n\n  // Implement intersection observer for lazy loading\n  const blockObserver = useCallback((node: HTMLDivElement | null) => {\n    if (!node) return;\n\n    const observer = new IntersectionObserver(\n      (entries) => {\n        entries.forEach(entry => {\n          if (entry.isIntersecting) {\n            const blockId = entry.target.getAttribute('data-block-id');\n            if (blockId) {\n              // Fix Set iteration by using current value to create a new Set\n              setVisibleBlocks(prev => {\n                const newSet = new Set(prev);\n                newSet.add(blockId);\n                return newSet;\n              });\n            }\n          }\n        });\n      },\n      { rootMargin: '200px 0px' } // Load blocks that are 200px outside viewport\n    );\n\n    observer.observe(node);\n\n    return () => observer.disconnect();\n  }, []);\n\n  // Cache check for block visibility\n  const isBlockVisible = useCallback((blockId: string) => {\n    return visibleBlocks.has(blockId);\n  }, [visibleBlocks]);\n\n  return (\n    <div className=\"block-library flex flex-col h-full overflow-hidden\">\n      {/* Search Box */}\n      <div className=\"px-3 py-2 border-b border-gray-200\">\n        <input\n          type=\"search\"\n          placeholder=\"Search blocks...\"\n          className=\"w-full px-3 py-1.5 text-sm border border-gray-300 rounded-md focus:outline-none focus:ring-1 focus:ring-blue-500\"\n          value={searchTerm}\n          onChange={(e) => setSearchTerm(e.target.value)}\n        />\n      </div>\n\n      {/* Category Tabs */}\n      <div className=\"flex flex-nowrap overflow-x-auto px-3 py-2 border-b border-gray-200 hide-scrollbar\">\n        {categories.map((category) => (\n          <button\n            key={category}\n            className={`px-3 py-1 mr-2 text-xs font-medium rounded-full whitespace-nowrap transition-colors ${\n              activeCategory === category || (category === 'All' && !activeCategory)\n                ? 'bg-blue-100 text-blue-700'\n                : 'bg-gray-100 text-gray-700 hover:bg-gray-200'\n            }`}\n            onClick={() => setActiveCategory(category === 'All' ? '' : category)}\n          >\n            {category}\n          </button>\n        ))}\n      </div>\n\n      {/* Block List - Virtualized */}\n      <div className=\"flex-1 overflow-y-auto p-3\">\n        {filteredBlocks.length > 0 ? (\n          filteredBlocks.map((block) => (\n            <div\n              key={block.blockId || block._id || `block-${Math.random()}`}\n              ref={blockObserver}\n              data-block-id={block.blockId || block._id || `block-${Math.random()}`}\n            >\n              <LibraryBlock\n                block={block}\n                onAddBlock={onAddBlock}\n                isVisible={isBlockVisible(block.blockId || block._id || '')}\n              />\n            </div>\n          ))\n        ) : (\n          <div className=\"text-sm text-gray-500 text-center p-4\">\n            No blocks found. Try a different search or category.\n          </div>\n        )}\n      </div>\n    </div>\n  );\n};\n\nexport default React.memo(BlockLibrary);\nexport { BlockLibrary };\n"], "mappings": ";;;AAAA;AACA;AACA;AACA;;AAEA,OAAOA,KAAK,IACVC,WAAW,EACXC,OAAO,EACPC,QAAQ,QACH,OAAO;AAEd,SAASC,OAAO,QAAQ,WAAW;;AAEnC;AAAA,SAAAC,MAAA,IAAAC,OAAA;AAGA;AACA,MAAMC,SAAS,GAAG;EAChBC,aAAa,EAAE;AACjB,CAAC;AAOD;AACA,MAAMC,gBAAgB,GAAIC,QAAgB,IAAa;EACrD,MAAMC,MAA8B,GAAG;IACrC,QAAQ,EAAE,2DAA2D;IACrE,SAAS,EAAE,uDAAuD;IAClE,QAAQ,EAAE,yDAAyD;IACnE,QAAQ,EAAE,2DAA2D;IACrE,KAAK,EAAE,qDAAqD;IAC5D,QAAQ,EAAE,uDAAuD;IACjE,SAAS,EAAE,2DAA2D;IACtE,YAAY,EAAE;EAChB,CAAC;EACD,OAAOA,MAAM,CAACD,QAAQ,CAAC,IAAI,uDAAuD;AACpF,CAAC;;AAED;AACA,MAAME,eAAe,GAAIF,QAAgB,IAAa;EACpD,MAAMG,KAA6B,GAAG;IACpC,QAAQ,EAAE,IAAI;IACd,SAAS,EAAE,IAAI;IACf,QAAQ,EAAE,IAAI;IACd,QAAQ,EAAE,IAAI;IACd,KAAK,EAAE,IAAI;IACX,QAAQ,EAAE,IAAI;IACd,SAAS,EAAE,KAAK;IAChB,YAAY,EAAE;EAChB,CAAC;EACD,OAAOA,KAAK,CAACH,QAAQ,CAAC,IAAI,IAAI;AAChC,CAAC;;AAED;AACA,MAAMI,YAIJ,gBAAAC,EAAA,cAAGf,KAAK,CAACgB,IAAI,CAAAC,EAAA,GAAAF,EAAA,CAAC,CAAC;EAAEG,KAAK;EAAEC,UAAU;EAAEC;AAAU,CAAC,KAAK;EAAAL,EAAA;EACpD,MAAM,CAAC;IAAEM;EAAW,CAAC,EAAEC,IAAI,CAAC,GAAGlB,OAAO,CAAC,OAAO;IAC5CmB,IAAI,EAAEhB,SAAS,CAACC,aAAa;IAC7BgB,IAAI,EAAE;MAAEN,KAAK;MAAEK,IAAI,EAAEhB,SAAS,CAACC;IAAc,CAAC;IAC9CiB,OAAO,EAAGC,OAAO,KAAM;MACrBL,UAAU,EAAEK,OAAO,CAACL,UAAU,CAAC;IACjC,CAAC;EACH,CAAC,CAAC,EAAE,CAACH,KAAK,CAAC,CAAC;;EAEZ;EACA,IAAI,CAACE,SAAS,EAAE;IACd,oBAAOd,OAAA;MAAKqB,SAAS,EAAC;IAAwC;MAAAC,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAAE,CAAC,CAAC,CAAC;EACrE;EAEA,oBACEzB,OAAA;IACE0B,GAAG,EAAEV,IAAK;IACVK,SAAS,EAAE,gJACTN,UAAU,GAAG,gDAAgD,GAAG,iBAAiB,EAChF;IACHY,OAAO,EAAEA,CAAA,KAAMd,UAAU,CAACD,KAAK,CAAE;IAAAgB,QAAA,gBAEjC5B,OAAA;MAAKqB,SAAS,EAAC,wCAAwC;MAAAO,QAAA,gBACrD5B,OAAA;QAAIqB,SAAS,EAAC,4CAA4C;QAAAO,QAAA,EAAEhB,KAAK,CAACiB;MAAI;QAAAP,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAK,CAAC,eAC5EzB,OAAA;QAAMqB,SAAS,EAAE,gDACflB,gBAAgB,CAACS,KAAK,CAACR,QAAQ,CAAC,EAC/B;QAAAwB,QAAA,EACAhB,KAAK,CAACR;MAAQ;QAAAkB,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACX,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACJ,CAAC,EAELb,KAAK,CAACkB,SAAS,gBACd9B,OAAA;MAAKqB,SAAS,EAAC,6FAA6F;MAAAO,QAAA,eAC1G5B,OAAA;QACE+B,GAAG,EAAEnB,KAAK,CAACkB,SAAU;QACrBE,GAAG,EAAEpB,KAAK,CAACiB,IAAK;QAChBR,SAAS,EAAC,sCAAsC;QAChDY,OAAO,EAAC;MAAM;QAAAX,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACf;IAAC;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACC,CAAC,gBAENzB,OAAA;MAAKqB,SAAS,EAAC,sEAAsE;MAAAO,QAAA,eACnF5B,OAAA;QAAKqB,SAAS,EAAC,aAAa;QAAAO,QAAA,gBAC1B5B,OAAA;UAAKqB,SAAS,EAAC,2EAA2E;UAAAO,QAAA,eACxF5B,OAAA;YAAKqB,SAAS,EAAC,uBAAuB;YAACa,IAAI,EAAC,MAAM;YAACC,MAAM,EAAC,cAAc;YAACC,OAAO,EAAC,WAAW;YAAAR,QAAA,eAC1F5B,OAAA;cAAMqC,aAAa,EAAC,OAAO;cAACC,cAAc,EAAC,OAAO;cAACC,WAAW,EAAE,CAAE;cAACC,CAAC,EAAC;YAAwJ;cAAAlB,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAE;UAAC;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAC7N;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACH,CAAC,eACNzB,OAAA;UAAKqB,SAAS,EAAC,uBAAuB;UAAAO,QAAA,EAAC;QAAU;UAAAN,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAK,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACpD;IAAC;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACH,CACN,EAEAb,KAAK,CAAC6B,WAAW,iBAChBzC,OAAA;MAAKqB,SAAS,EAAC,yCAAyC;MAAAO,QAAA,EACrDhB,KAAK,CAAC6B;IAAW;MAAAnB,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACf,CACN,EAGAV,UAAU,iBACTf,OAAA;MAAKqB,SAAS,EAAC,wFAAwF;MAAAO,QAAA,eACrG5B,OAAA;QAAKqB,SAAS,EAAC,mCAAmC;QAAAO,QAAA,EAAC;MAAW;QAAAN,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAK;IAAC;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACjE,CACN;EAAA;IAAAH,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OACE,CAAC;AAEV,CAAC;EAAA,QAlEgC3B,OAAO;AAAA,EAkEvC,CAAC;EAAA,QAlE+BA,OAAO;AAAA,EAkEtC;;AAEF;AAAA4C,GAAA,GAzEMlC,YAIJ;AAsEF,MAAMmC,YAAyC,GAAGA,CAAC;EAAEC,MAAM;EAAE/B;AAAW,CAAC,KAAK;EAAAgC,GAAA;EAC5E,MAAM,CAACC,cAAc,EAAEC,iBAAiB,CAAC,GAAGlD,QAAQ,CAAS,EAAE,CAAC;EAChE,MAAM,CAACmD,UAAU,EAAEC,aAAa,CAAC,GAAGpD,QAAQ,CAAS,EAAE,CAAC;EACxD,MAAM,CAACqD,aAAa,EAAEC,gBAAgB,CAAC,GAAGtD,QAAQ,CAAc,IAAIuD,GAAG,CAAC,CAAC,CAAC;;EAE1E;EACA,MAAMC,UAAU,GAAGzD,OAAO,CAAC,MAAM;IAC/B,MAAM0D,IAAI,GAAG,IAAIF,GAAG,CAAS,CAAC;IAC9BR,MAAM,CAACW,OAAO,CAAC3C,KAAK,IAAI;MACtB,IAAIA,KAAK,CAACR,QAAQ,EAAEkD,IAAI,CAACE,GAAG,CAAC5C,KAAK,CAACR,QAAQ,CAAC;IAC9C,CAAC,CAAC;IACF,OAAO,CAAC,KAAK,EAAE,GAAGqD,KAAK,CAACC,IAAI,CAACJ,IAAI,CAAC,CAAC;EACrC,CAAC,EAAE,CAACV,MAAM,CAAC,CAAC;;EAEZ;EACA,MAAMe,cAAc,GAAG/D,OAAO,CAAC,MAAM;IACnC,OAAOgD,MAAM,CAACgB,MAAM,CAAChD,KAAK,IAAI;MAC5B;MACA,IAAIkC,cAAc,IAAIA,cAAc,KAAK,KAAK,IAAIlC,KAAK,CAACR,QAAQ,KAAK0C,cAAc,EAAE;QACnF,OAAO,KAAK;MACd;;MAEA;MACA,IAAIE,UAAU,CAACa,IAAI,CAAC,CAAC,KAAK,EAAE,EAAE;QAC5B,MAAMC,WAAW,GAAGd,UAAU,CAACe,WAAW,CAAC,CAAC;QAC5C,OACEnD,KAAK,CAACiB,IAAI,CAACkC,WAAW,CAAC,CAAC,CAACC,QAAQ,CAACF,WAAW,CAAC,IAC9C,CAAClD,KAAK,CAAC6B,WAAW,IAAI,EAAE,EAAEsB,WAAW,CAAC,CAAC,CAACC,QAAQ,CAACF,WAAW,CAAC,IAC7D,CAAClD,KAAK,CAACR,QAAQ,IAAI,EAAE,EAAE2D,WAAW,CAAC,CAAC,CAACC,QAAQ,CAACF,WAAW,CAAC;MAE9D;MAEA,OAAO,IAAI;IACb,CAAC,CAAC;EACJ,CAAC,EAAE,CAAClB,MAAM,EAAEE,cAAc,EAAEE,UAAU,CAAC,CAAC;;EAExC;EACA,MAAMiB,aAAa,GAAGtE,WAAW,CAAEuE,IAA2B,IAAK;IACjE,IAAI,CAACA,IAAI,EAAE;IAEX,MAAMC,QAAQ,GAAG,IAAIC,oBAAoB,CACtCC,OAAO,IAAK;MACXA,OAAO,CAACd,OAAO,CAACe,KAAK,IAAI;QACvB,IAAIA,KAAK,CAACC,cAAc,EAAE;UACxB,MAAMC,OAAO,GAAGF,KAAK,CAACG,MAAM,CAACC,YAAY,CAAC,eAAe,CAAC;UAC1D,IAAIF,OAAO,EAAE;YACX;YACArB,gBAAgB,CAACwB,IAAI,IAAI;cACvB,MAAMC,MAAM,GAAG,IAAIxB,GAAG,CAACuB,IAAI,CAAC;cAC5BC,MAAM,CAACpB,GAAG,CAACgB,OAAO,CAAC;cACnB,OAAOI,MAAM;YACf,CAAC,CAAC;UACJ;QACF;MACF,CAAC,CAAC;IACJ,CAAC,EACD;MAAEC,UAAU,EAAE;IAAY,CAAC,CAAC;IAC9B,CAAC;IAEDV,QAAQ,CAACW,OAAO,CAACZ,IAAI,CAAC;IAEtB,OAAO,MAAMC,QAAQ,CAACY,UAAU,CAAC,CAAC;EACpC,CAAC,EAAE,EAAE,CAAC;;EAEN;EACA,MAAMC,cAAc,GAAGrF,WAAW,CAAE6E,OAAe,IAAK;IACtD,OAAOtB,aAAa,CAAC+B,GAAG,CAACT,OAAO,CAAC;EACnC,CAAC,EAAE,CAACtB,aAAa,CAAC,CAAC;EAEnB,oBACElD,OAAA;IAAKqB,SAAS,EAAC,oDAAoD;IAAAO,QAAA,gBAEjE5B,OAAA;MAAKqB,SAAS,EAAC,oCAAoC;MAAAO,QAAA,eACjD5B,OAAA;QACEiB,IAAI,EAAC,QAAQ;QACbiE,WAAW,EAAC,kBAAkB;QAC9B7D,SAAS,EAAC,kHAAkH;QAC5H8D,KAAK,EAAEnC,UAAW;QAClBoC,QAAQ,EAAGC,CAAC,IAAKpC,aAAa,CAACoC,CAAC,CAACZ,MAAM,CAACU,KAAK;MAAE;QAAA7D,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAChD;IAAC;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACC,CAAC,eAGNzB,OAAA;MAAKqB,SAAS,EAAC,oFAAoF;MAAAO,QAAA,EAChGyB,UAAU,CAACiC,GAAG,CAAElF,QAAQ,iBACvBJ,OAAA;QAEEqB,SAAS,EAAE,uFACTyB,cAAc,KAAK1C,QAAQ,IAAKA,QAAQ,KAAK,KAAK,IAAI,CAAC0C,cAAe,GAClE,2BAA2B,GAC3B,6CAA6C,EAChD;QACHnB,OAAO,EAAEA,CAAA,KAAMoB,iBAAiB,CAAC3C,QAAQ,KAAK,KAAK,GAAG,EAAE,GAAGA,QAAQ,CAAE;QAAAwB,QAAA,EAEpExB;MAAQ,GARJA,QAAQ;QAAAkB,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OASP,CACT;IAAC;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACC,CAAC,eAGNzB,OAAA;MAAKqB,SAAS,EAAC,4BAA4B;MAAAO,QAAA,EACxC+B,cAAc,CAAC4B,MAAM,GAAG,CAAC,GACxB5B,cAAc,CAAC2B,GAAG,CAAE1E,KAAK,iBACvBZ,OAAA;QAEE0B,GAAG,EAAEuC,aAAc;QACnB,iBAAerD,KAAK,CAAC4D,OAAO,IAAI5D,KAAK,CAAC4E,GAAG,IAAI,SAASC,IAAI,CAACC,MAAM,CAAC,CAAC,EAAG;QAAA9D,QAAA,eAEtE5B,OAAA,CAACQ,YAAY;UACXI,KAAK,EAAEA,KAAM;UACbC,UAAU,EAAEA,UAAW;UACvBC,SAAS,EAAEkE,cAAc,CAACpE,KAAK,CAAC4D,OAAO,IAAI5D,KAAK,CAAC4E,GAAG,IAAI,EAAE;QAAE;UAAAlE,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAC7D;MAAC,GARGb,KAAK,CAAC4D,OAAO,IAAI5D,KAAK,CAAC4E,GAAG,IAAI,SAASC,IAAI,CAACC,MAAM,CAAC,CAAC,EAAE;QAAApE,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OASxD,CACN,CAAC,gBAEFzB,OAAA;QAAKqB,SAAS,EAAC,uCAAuC;QAAAO,QAAA,EAAC;MAEvD;QAAAN,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAK;IACN;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACE,CAAC;EAAA;IAAAH,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OACH,CAAC;AAEV,CAAC;AAACoB,GAAA,CA3HIF,YAAyC;AAAAgD,GAAA,GAAzChD,YAAyC;AA6H/C,eAAAiD,GAAA,gBAAelG,KAAK,CAACgB,IAAI,CAACiC,YAAY,CAAC;AACvC,SAASA,YAAY;AAAG,IAAAhC,EAAA,EAAA+B,GAAA,EAAAiD,GAAA,EAAAC,GAAA;AAAAC,YAAA,CAAAlF,EAAA;AAAAkF,YAAA,CAAAnD,GAAA;AAAAmD,YAAA,CAAAF,GAAA;AAAAE,YAAA,CAAAD,GAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}