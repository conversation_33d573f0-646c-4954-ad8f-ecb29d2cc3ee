@import url('https://fonts.googleapis.com/css2?family=Inter:wght@300;400;500;600;700&display=swap');
@import url('https://fonts.googleapis.com/css2?family=Source+Sans+Pro:wght@300;400;600;700&display=swap');

@tailwind base;
@tailwind components;
@tailwind utilities;

/* Import Email Editor Styles */
@import './styles/editor.css';

/* --- Professional Blue Theme --- */
:root {
  /* --- Core Palette --- */
  --color-primary-blue: #1E5DA6;     /* Professional Blue */
  --color-primary-blue-light: #3B82F6;/* Brighter Blue */
  --color-accent-coral: #4F94DD;    /* Secondary Blue */
  --color-accent-coral-hover: #6BAAEF;/* Lighter Secondary Blue */
  --color-growth-green: #2F9E44;     /* Success Green */
  --color-neutral-base: #111827;    /* Dark BG */
  --color-neutral-light: #1F2937;   /* Medium Dark BG */
  --color-border: #374151;         /* Border Gray */
  --color-text-primary: #F3F4F6;   /* Light Text */
  --color-text-secondary: #9CA3AF; /* Medium Gray Text */
  --color-danger: #DC2626;        /* Danger Red */
  --color-glass-bg: rgba(17, 24, 39, 0.85); /* Dark Glass BG */
  --color-glass-border: rgba(75, 85, 99, 0.3); /* Border for glass */

  /* --- Fonts --- */
  --font-sans: 'Inter', 'Source Sans Pro', sans-serif;
}

/* Subtle Background Animation */
@keyframes gradientShift {
  0% {
    background-position: 0% 50%;
  }
  50% {
    background-position: 100% 50%;
  }
  100% {
    background-position: 0% 50%;
  }
}

body {
  margin: 0;
  background-color: var(--color-neutral-base);
  background-image: linear-gradient(170deg, var(--color-neutral-base) 0%, rgba(28, 36, 55, 0.95) 100%);
  background-attachment: fixed;
  color: var(--color-text-primary);
  font-family: var(--font-sans);
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: 16px;
  line-height: 1.6;
  min-height: 100vh;
  position: relative;
  letter-spacing: 0.01em;
}

/* Subtle pattern overlay */
body::before {
  content: "";
  position: fixed;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  opacity: 0.03;
  background-image: url("data:image/svg+xml,%3Csvg width='60' height='60' viewBox='0 0 60 60' xmlns='http://www.w3.org/2000/svg'%3E%3Cpath d='M54.627 0l.83.828-1.415 1.415L51.8 0h2.827zM5.373 0l-.83.828L5.96 2.243 8.2 0H5.374zM48.97 0l3.657 3.657-1.414 1.414L46.143 0h2.828zM11.03 0L7.372 3.657l1.415 1.414L13.857 0H11.03zm32.284 0L49.8 6.485 48.384 7.9l-7.9-7.9h2.83zM16.686 0L10.2 6.485 11.616 7.9l7.9-7.9h-2.83zm20.97 0l9.315 9.314-1.414 1.414L34.828 0h2.83zM22.344 0L13.03 9.314l1.414 1.414L25.172 0h-2.83zM32 0l12.142 12.142-1.414 1.414L30 .828 17.272 13.556l-1.414-1.414L28 0h4zM.284 0l28 28-1.414 1.414L0 2.544V0h.284zM0 5.373l25.456 25.455-1.414 1.415L0 8.2V5.374zm0 5.656l22.627 22.627-1.414 1.414L0 13.86v-2.83zm0 5.656l19.8 19.8-1.415 1.413L0 19.514v-2.83zm0 5.657l16.97 16.97-1.414 1.415L0 25.172v-2.83zM0 28l14.142 14.142-1.414 1.414L0 30.828V28zm0 5.657L11.314 44.97l-1.414 1.414L0 36.485v-2.83zm0 5.657L8.485 47.8l-1.414 1.414L0 42.143v-2.83zm0 5.657l5.657 5.657-1.414 1.415L0 47.8v-2.83zm0 5.657l2.828 2.83-1.414 1.413L0 53.458v-2.83zM54.627 60L30 35.373 5.373 60H8.2L30 38.2 51.8 60h2.827zm-5.656 0L30 41.03 11.03 60h2.828L30 43.858 46.142 60h2.83zm-5.656 0L30 46.686 16.686 60h2.83L30 49.515 40.485 60h2.83zm-5.657 0L30 52.343 22.344 60h2.83L30 55.172 34.828 60h2.83zM32 60l-2-2-2 2h4zM59.716 0l-28 28 1.414 1.414L60 2.544V0h-.284zM60 5.373L34.544 30.828l1.414 1.415L60 8.2V5.374zm0 5.656L37.373 33.656l1.414 1.414L60 13.86v-2.83zm0 5.656l-19.8 19.8 1.415 1.413L60 19.514v-2.83zm0 5.657l-16.97 16.97 1.414 1.415L60 25.172v-2.83zM60 28L45.858 42.142l1.414 1.414L60 30.828V28zm0 5.657L48.686 44.97l1.414 1.414L60 36.485v-2.83zm0 5.657L51.515 47.8l1.414 1.414L60 42.143v-2.83zm0 5.657l-5.657 5.657 1.414 1.415L60 47.8v-2.83zm0 5.657l-2.828 2.83 1.414 1.413L60 53.458v-2.83zM39.9 16.385l1.414-1.414L30 3.658 18.686 14.97l1.415 1.415 9.9-9.9 9.9 9.9zm-2.83 2.828l1.415-1.414L30 9.313 21.515 17.8l1.414 1.413L30 11.97l7.07 7.242zm-2.827 2.83l1.414-1.416L30 14.97l-5.657 5.657 1.414 1.415L30 17.8l4.243 4.242zm-2.83 2.827l1.415-1.414L30 20.626l-2.828 2.83 1.414 1.414L30 23.456l1.414 1.414zM56.87 59.414L58.284 58 30 29.716 1.716 58l1.414 1.414L30 32.544l26.87 26.87z' fill='%233B82F6' fill-opacity='0.05' fill-rule='evenodd'/%3E%3C/svg%3E");
  pointer-events: none;
  z-index: -1;
}

/* Headings: Professional styling */
h1, h2, h3, h4, h5, h6 {
  font-family: var(--font-sans);
  color: var(--color-text-primary);
  font-weight: 600;
  line-height: 1.3;
  margin-bottom: 0.75rem;
  letter-spacing: 0;
}

h1 {
  font-size: 1.875rem;
  font-weight: 700;
}

h2 {
  font-size: 1.5rem;
}

h3 {
  font-size: 1.25rem;
}

/* Links: Professional blue with subtle hover effect */
a {
  color: var(--color-primary-blue-light);
  text-decoration: none;
  font-weight: 500;
  transition: all 0.2s ease;
  position: relative;
}
a:hover {
  color: var(--color-accent-coral-hover);
}
a:hover::after {
  content: "";
  position: absolute;
  height: 1px;
  width: 100%;
  background-color: var(--color-accent-coral-hover);
  bottom: -2px;
  left: 0;
}

/* Input fields: Clean, professional styling */
input,
select,
textarea {
    @apply block w-full rounded-md shadow-sm placeholder-gray-400 text-base border-transparent;
    background-color: rgba(31, 41, 55, 0.8);
    border: 1px solid var(--color-border);
    color: var(--color-text-primary);
    padding: 0.625rem 0.75rem;
    transition: all 0.2s ease;
    font-family: var(--font-sans);
}
input:focus,
select:focus,
textarea:focus {
    border-color: var(--color-primary-blue-light);
    box-shadow: 0 0 0 1px var(--color-primary-blue-light), 0 0 0 3px rgba(59, 130, 246, 0.2);
    outline: none;
    background-color: rgba(31, 41, 55, 0.95);
}

/* Placeholder text */
input::placeholder,
textarea::placeholder,
select::placeholder {
  color: rgba(156, 163, 175, 0.6);
}

/* Button styles with professional appearance */
button,
.btn {
    @apply inline-flex items-center justify-center px-4 py-2 rounded-md font-medium text-sm
           border border-transparent shadow-sm
           transition-all ease-in-out duration-150 disabled:opacity-50 disabled:cursor-not-allowed;
    background-color: var(--color-primary-blue);
    border: 1px solid transparent;
    color: var(--color-text-primary);
    font-family: var(--font-sans);
}
button:hover,
.btn:hover {
    background-color: #1a4f8f;
    transform: translateY(-1px);
    box-shadow: 0 4px 6px rgba(0, 0, 0, 0.1);
}
button:focus,
.btn:focus {
    outline: none;
    box-shadow: 0 0 0 2px var(--color-primary-blue-light), 0 0 0 4px rgba(59, 130, 246, 0.3);
}
button:disabled,
.btn:disabled {
    background-color: var(--color-neutral-light);
    border-color: var(--color-border);
    color: var(--color-text-secondary);
    box-shadow: none;
    transform: translateY(0);
}

/* Cards/Containers: Professional styling */
.card, .container-futuristic {
  background-color: rgba(31, 41, 55, 0.95);
  border: 1px solid var(--color-border);
  border-radius: 0.375rem;
  padding: 1.5rem;
  box-shadow: 0 4px 6px rgba(0, 0, 0, 0.1), 0 1px 3px rgba(0, 0, 0, 0.1);
  position: relative;
}

/* Glassmorphic Style for professional UI */
.glassmorphic {
  background: var(--color-glass-bg);
  backdrop-filter: blur(8px);
  -webkit-backdrop-filter: blur(8px);
  border: 1px solid var(--color-glass-border);
  box-shadow: 0 4px 6px rgba(0, 0, 0, 0.1), 0 1px 3px rgba(0, 0, 0, 0.1);
}

code {
  font-family: ui-monospace, SFMono-Regular, Menlo, Monaco, Consolas, "Liberation Mono", "Courier New", monospace;
  background-color: rgba(31, 41, 55, 0.5);
  color: var(--color-text-primary);
  padding: 0.2em 0.4em;
  border-radius: 4px;
  font-size: 0.875em;
}

/* --- Specific Component Class Styles --- */

/* Primary Button */
.btn-primary {
   background-color: var(--color-primary-blue);
   border-color: transparent;
   color: white;
}
.btn-primary:hover {
   background-color: #1a4f8f;
}
.btn-primary:focus {
   box-shadow: 0 0 0 2px rgba(59, 130, 246, 0.5);
}

/* CTA Button Variant */
.btn-accent, .btn-cta {
   background-color: var(--color-accent-coral);
   border-color: transparent;
   color: white;
   font-weight: 600;
}
.btn-accent:hover, .btn-cta:hover {
   background-color: #3782d1;
}
.btn-accent:focus, .btn-cta:focus {
   box-shadow: 0 0 0 2px rgba(59, 130, 246, 0.5);
}

/* Secondary Button */
.btn-secondary {
   background-color: transparent;
   border-color: var(--color-border);
   color: var(--color-text-primary);
}
.btn-secondary:hover {
   background-color: rgba(75, 85, 99, 0.1);
   border-color: var(--color-text-secondary);
   color: var(--color-text-primary);
}

/* Danger Button */
.btn-danger {
   background-color: var(--color-danger);
   border-color: transparent;
   color: white;
}
.btn-danger:hover {
   background-color: #b91c1c;
}

/* Label Style */
.form-label {
  @apply block mb-1.5 text-sm font-medium;
  color: var(--color-text-secondary);
  letter-spacing: 0.01em;
}

/* Table Styles: Professional */
.table-container {
  @apply overflow-x-auto rounded-md border;
  border-color: var(--color-border);
  background-color: rgba(31, 41, 55, 0.95);
}

.table {
  @apply min-w-full divide-y;
  divide-color: var(--color-border);
}

.table th {
  @apply px-6 py-3 text-left text-xs font-medium uppercase tracking-wider;
  background-color: rgba(31, 41, 55, 0.7);
  color: var(--color-text-secondary);
  letter-spacing: 0.05em;
  font-weight: 600;
  border-bottom: 1px solid var(--color-border);
}

.table td {
  @apply px-6 py-4 whitespace-nowrap text-sm;
  color: var(--color-text-primary);
  border-color: var(--color-border);
  transition: background-color 0.15s ease-in;
  border-bottom: 1px solid rgba(55, 65, 81, 0.5);
}
.table tbody tr:hover td {
  background-color: rgba(55, 65, 81, 0.3);
}

/* Stat Card adjustments */
.stat-card {
  @apply flex flex-col;
}
.stat-value {
  @apply text-2xl font-semibold mt-1;
  color: var(--color-text-primary);
}
.stat-label {
  @apply text-sm uppercase tracking-wide;
  color: var(--color-text-secondary);
  letter-spacing: 0.05em;
}

/* Utility for growth indicators */
.text-growth {
  color: var(--color-growth-green);
}
.bg-growth-light {
  background-color: rgba(47, 158, 68, 0.1);
}

/* Custom scrollbar for professional theme */
::-webkit-scrollbar {
  width: 8px;
  height: 8px;
}
::-webkit-scrollbar-track {
  background: var(--color-neutral-base);
}
::-webkit-scrollbar-thumb {
  background: #4B5563;
  border-radius: 4px;
}
::-webkit-scrollbar-thumb:hover {
  background: #6B7280;
}
