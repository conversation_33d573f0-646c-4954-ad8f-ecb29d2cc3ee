/**
 * Enhanced EmailEditor component for Driftly Email Generator
 * Provides advanced drag-and-drop interface for template creation and editing
 * Features: Real-time preview, responsive design, advanced block library, undo/redo
 */

import React, {
  useCallback,
  useEffect,
  useMemo,
  useRef,
  useState,
} from 'react';

import mjml2html from 'mjml-browser';
import {
  DropTargetMonitor,
  useDrag,
  useDrop,
} from 'react-dnd';
import {
  useNavigate,
  useParams,
} from 'react-router-dom';

import api from '../services/api'; // Corrected import path
// Types - Adjust path as needed
import {
  Block,
  BlockContent,
  BlockListResponse,
  Template,
  TemplateDetailResponse,
  TemplateSaveResponse,
  UserPreference,
} from '../types/editor';
import BlockEditor from './BlockEditor';
// Components - Adjust paths as needed
import BlockLibrary from './BlockLibrary';
import EmailPreview from './EmailPreview';
import SaveTemplateModal from './SaveTemplateModal';

// Styles - Ensure this is imported in your main application entry point
// e.g., import '../styles/editor.css';

const API_URL = process.env.REACT_APP_API_URL || '/api'; // Use environment variable

// Define Drag Item Types
const ItemTypes = {
  BLOCK: 'block',
  LIBRARY_BLOCK: 'library_block'
};

interface EditorDragItem {
  index: number;
  type: typeof ItemTypes.BLOCK;
  id: string; // Unique instance ID of the block being dragged
}

interface LibraryDragItem {
  block: Block; // The block definition from the library
  type: typeof ItemTypes.LIBRARY_BLOCK;
}

// Near the top of the file, add a template cache mechanism:
// Cache for storing generated HTML previews
const templateCache = new Map<string, {
  html: string;
  timestamp: number;
  mjml: string;
}>();

// Cache TTL (Time To Live) in milliseconds - 5 minutes
const CACHE_TTL = 5 * 60 * 1000;

// Add this function before the EmailEditor component definition
// Function to generate a cache key based on blocks
const generateCacheKey = (blocks: Block[]): string => {
  return blocks.map(block => {
    const { instanceId, content } = block;
    // Include only essential data in cache key
    return `${block.blockId || block._id}:${instanceId}:${JSON.stringify(content)}`;
  }).join('|');
};

// Function to clear stale cache entries
const clearStaleCache = () => {
  const now = Date.now();
  templateCache.forEach((entry, key) => {
    if (now - entry.timestamp > CACHE_TTL) {
      templateCache.delete(key);
    }
  });
};

// Enhanced state management with undo/redo functionality
interface EditorState {
  blocks: Block[];
  selectedBlockIndex: number | null;
  timestamp: number;
}

interface UndoRedoState {
  history: EditorState[];
  currentIndex: number;
  maxHistorySize: number;
}

// --- EmailEditor Component ---

const EmailEditor: React.FC = () => {
  const { templateId } = useParams<{ templateId?: string }>();
  const navigate = useNavigate();

  // Core State
  const [template, setTemplate] = useState<Partial<Template>>({});
  const [editorBlocks, setEditorBlocks] = useState<Block[]>([]);
  const [availableBlocks, setAvailableBlocks] = useState<Block[]>([]);
  const [selectedBlockIndex, setSelectedBlockIndex] = useState<number | null>(null);
  const [previewHtml, setPreviewHtml] = useState<string>('');
  const [previewMode, setPreviewMode] = useState<'desktop' | 'mobile'>('desktop');
  const [isSaving, setIsSaving] = useState<boolean>(false);
  const [showSaveModal, setShowSaveModal] = useState<boolean>(false);
  const [isLoading, setIsLoading] = useState<boolean>(true);
  const [error, setError] = useState<string | null>(null);
  const [saveError, setSaveError] = useState<string | null>(null);
  const [userPreferences, setUserPreferences] = useState<UserPreference | null>(null);
  const [templateName, setTemplateName] = useState('');

  // Enhanced UI State
  const [isDragActive, setIsDragActive] = useState<boolean>(false);
  const [showBlockLibrary, setShowBlockLibrary] = useState<boolean>(true);
  const [showPreview, setShowPreview] = useState<boolean>(true);
  const [isGeneratingPreview, setIsGeneratingPreview] = useState<boolean>(false);
  const [viewMode, setViewMode] = useState<'edit' | 'preview' | 'split'>('split');

  // Undo/Redo State
  const [undoRedoState, setUndoRedoState] = useState<UndoRedoState>({
    history: [],
    currentIndex: -1,
    maxHistorySize: 50
  });

  // Performance optimization - memoized values
  const canUndo = useMemo(() => undoRedoState.currentIndex > 0, [undoRedoState.currentIndex]);
  const canRedo = useMemo(() => undoRedoState.currentIndex < undoRedoState.history.length - 1, [undoRedoState.currentIndex, undoRedoState.history.length]);

  // Refs
  const previewIframeRef = useRef<HTMLIFrameElement>(null);
  const editorCanvasRef = useRef<HTMLDivElement>(null);

  // --- Undo/Redo Functions --- //
  const saveToHistory = useCallback((blocks: Block[], selectedIndex: number | null) => {
    setUndoRedoState(prev => {
      const newState: EditorState = {
        blocks: JSON.parse(JSON.stringify(blocks)), // Deep clone
        selectedBlockIndex: selectedIndex,
        timestamp: Date.now()
      };

      // Remove any future history if we're not at the end
      const newHistory = prev.history.slice(0, prev.currentIndex + 1);
      newHistory.push(newState);

      // Limit history size
      if (newHistory.length > prev.maxHistorySize) {
        newHistory.shift();
      }

      return {
        ...prev,
        history: newHistory,
        currentIndex: newHistory.length - 1
      };
    });
  }, []);

  const undo = useCallback(() => {
    if (!canUndo) return;

    setUndoRedoState(prev => {
      const newIndex = prev.currentIndex - 1;
      const state = prev.history[newIndex];

      if (state) {
        setEditorBlocks(state.blocks);
        setSelectedBlockIndex(state.selectedBlockIndex);
      }

      return {
        ...prev,
        currentIndex: newIndex
      };
    });
  }, [canUndo]);

  const redo = useCallback(() => {
    if (!canRedo) return;

    setUndoRedoState(prev => {
      const newIndex = prev.currentIndex + 1;
      const state = prev.history[newIndex];

      if (state) {
        setEditorBlocks(state.blocks);
        setSelectedBlockIndex(state.selectedBlockIndex);
      }

      return {
        ...prev,
        currentIndex: newIndex
      };
    });
  }, [canRedo]);

  // Keyboard shortcuts for undo/redo
  useEffect(() => {
    const handleKeyDown = (e: KeyboardEvent) => {
      if ((e.ctrlKey || e.metaKey) && e.key === 'z' && !e.shiftKey) {
        e.preventDefault();
        undo();
      } else if ((e.ctrlKey || e.metaKey) && (e.key === 'y' || (e.key === 'z' && e.shiftKey))) {
        e.preventDefault();
        redo();
      }
    };

    window.addEventListener('keydown', handleKeyDown);
    return () => window.removeEventListener('keydown', handleKeyDown);
  }, [undo, redo]);

  // --- Data Fetching --- //
  useEffect(() => {
    let isMounted = true;
    const fetchData = async () => {
      setIsLoading(true);
      setError(null);
      try {
        // Fetch available blocks first (always needed)
        console.log(`Fetching blocks from ${API_URL}/blocks`);
        // Use the shared api instance with BlockListResponse type
        const blocksPromise = api.get<BlockListResponse>('/blocks');

        // Fetch user preferences to potentially apply brand colors/fonts later
        console.log(`Fetching user preferences from ${API_URL}/user/preferences`);
         // Use the shared api instance with correct inline type
        const prefsPromise = api.get<{ preferences: UserPreference }>(`/user/preferences`);

        // Fetch template data if ID exists
        const templatePromise = templateId
          // Use the shared api instance with TemplateDetailResponse type
          ? api.get<TemplateDetailResponse>(`/templates/${templateId}`)
          : Promise.resolve(null);

        const [blocksResponse, prefsResponse, templateResponse] = await Promise.all([
          blocksPromise,
          prefsPromise,
          templatePromise
        ]);

        // Access blocks correctly from response.data.data
        const fetchedAvailableBlocks = blocksResponse.data.data || [];
        console.log(`Fetched ${fetchedAvailableBlocks.length} available blocks.`);
        setAvailableBlocks(fetchedAvailableBlocks);

        // Process user preferences
        const userPreferences = prefsResponse.data.preferences; // Store preferences
        // TODO: Apply user preferences (e.g., brand colors, fonts)

        if (templateResponse && templateResponse.data.template) {
          const templateData = templateResponse.data.template; // Correct path now
          console.log('[EmailEditor] Fetched template data:', JSON.stringify(templateData, null, 2)); // Log fetched data

          // Apply brand colors/fonts from preferences if not set on template
          setTemplate({
              ...templateData,
              brandColors: templateData.brandColors || userPreferences?.brandColors,
              defaultFonts: templateData.defaultFonts || userPreferences?.defaultFonts,
          });

          // Populate editor blocks based on blockIds and fetched definitions
          console.log('[EmailEditor] Attempting to populate blocks. Available block defs:', fetchedAvailableBlocks.length);
          console.log('[EmailEditor] Template blockIds:', templateData.blockIds);
          console.log('[EmailEditor] Template blocks data:', templateData.blocks);

          if (templateData.blockIds && templateData.blockIds.length > 0 && fetchedAvailableBlocks.length > 0) {
            console.log('[EmailEditor] Conditions met, proceeding to map blockIds.');
            const populatedBlocks = templateData.blockIds.map((id: string, index: number) => {
              console.log(`[EmailEditor] Mapping blockId: ${id} at index: ${index}`);
              // Match ID from templateData.blockIds with fetchedAvailableBlocks
              const foundBlockDef = fetchedAvailableBlocks.find((b: Block) => b.blockId === id || b._id === id); // Check both blockId and _id
              console.log(`[EmailEditor] ... Definition found for ${id}?`, foundBlockDef ? 'Yes' : 'No');

              if (!foundBlockDef) {
                console.warn(`[EmailEditor] Block definition not found for available block matching ID: ${id}. Skipping.`); // Refined warning
                return null;
              }

              // Get content for this specific instance from templateData.blocks
              const instanceBlockData = templateData.blocks?.[index]; // Get the block data saved for this instance
              const instanceContent = instanceBlockData?.content || foundBlockDef.content || {}; // Use instance content, fallback to definition's default
              console.log(`[EmailEditor] ... Instance content for ${id}:`, instanceContent);

              const instanceId = `${id}-${index}-${Date.now()}-${Math.random().toString(36).substring(7)}`; // Unique key for React

              // Return the combined block object for the editor state
              const blockForState = {
                  ...foundBlockDef, // Base definition (MJML, name, category etc.)
                  content: { ...instanceContent }, // Specific content for this instance
                  instanceId // Unique ID for this instance in the editor
              };
              console.log(`[EmailEditor] ... Created block object for state for ${id}:`, blockForState);
              return blockForState;
            }).filter(b => b !== null);

            console.log("[EmailEditor] Populated blocks array before setting state:", populatedBlocks);
            setEditorBlocks(populatedBlocks as Block[]);
          } else {
            console.log("[EmailEditor] Condition for populating blocks NOT met:", {
                hasBlockIds: !!templateData.blockIds,
                blockIdsLength: templateData.blockIds?.length,
                hasFetchedBlocks: !!fetchedAvailableBlocks,
                fetchedBlocksLength: fetchedAvailableBlocks?.length
            });
            setEditorBlocks([]); // Sets blocks to empty
          }
        } else {
          // New template: initialize with preferences
          console.log('Initializing new template state with preferences.');
          setTemplate({
            templateName: 'Untitled Template',
            subject: 'Your Subject Here',
            brandColors: userPreferences?.brandColors,
            defaultFonts: userPreferences?.defaultFonts,
            // Initialize other necessary fields if needed
            userId: userPreferences?.userId // Important for saving later
          });
          setEditorBlocks([]);
        }
      } catch (err: any) {
        console.error('Error loading editor data:', err);
        let errorMsg = 'Failed to load editor data.'; // Default message

        if (err.response) {
          // Check if it's the template fetch that failed with 404
          if (err.config?.url?.includes(`/templates/${templateId}`) && err.response.status === 404) {
            errorMsg = `Template with ID ${templateId} not found. It may have been deleted.`;
            // Optionally, redirect the user or clear the template state
            // navigate('/templates'); // Example redirect
            setTemplate({}); // Clear any partial template data
            setEditorBlocks([]);
          } else {
            // Use the error message from the response if available, otherwise use the generic message
            errorMsg = err.response.data?.error || err.response.data?.message || err.message || errorMsg;
          }
        } else {
          // Network error or other issue
          errorMsg = err.message || errorMsg;
        }

        setError(errorMsg);
        // Fallback logic: Check if availableBlocks is empty (meaning block fetch might have also failed)
        if (availableBlocks.length === 0) {
            console.warn("Falling back to empty block list as blocks couldn't be fetched or list is empty.");
            setAvailableBlocks([]); // Ensure it's an empty array if blocks failed
        }
      } finally {
        setIsLoading(false);
      }
    };

    fetchData();

    return () => {
      isMounted = false;
    };
  // eslint-disable-next-line
  }, [templateId]); // Rerun only when templateId changes

  // --- MJML Generation & Preview Update --- //
  const generateMjmlFromBlocks = useCallback((blockList: Block[]): string => {
    // If there are no blocks, return early with an empty string
    if (!blockList.length) return '';

    // Generate a cache key for this block configuration
    const cacheKey = generateCacheKey(blockList);

    // Check if we have a cached version
    if (templateCache.has(cacheKey)) {
      const cached = templateCache.get(cacheKey);
      if (cached && (Date.now() - cached.timestamp) < CACHE_TTL) {
        console.log("[EmailEditor] Using cached MJML");
        return cached.mjml;
      }
    }

    // Periodically clean up old cache entries
    clearStaleCache();

    // Process blocks in chunks if there are many
    const chunkSize = 5;
    let mjmlBodyContent = '';

    for (let i = 0; i < blockList.length; i += chunkSize) {
      const chunk = blockList.slice(i, i + chunkSize);
      const chunkContent = chunk.map(block => {
        let blockMjml = block.mjml || '';
        const content = block.content || {};

        // More robust replacement logic
        Object.keys(content).forEach(key => {
          const value = content[key];
          const placeholder = `{{${key}}}`; // Standard placeholder

          // Only process if value exists
          if (value !== undefined && value !== null) {
            // Handle arrays specifically (e.g., nav links, social icons)
            if (Array.isArray(value)) {
              // Existing array handling code...
              if (key === 'nav_links' || key === 'navLinks') {
                const linksHtml = value.map((link: any) =>
                  `<mj-navbar-link href="${link.url || '#'}" color="#1E3A8A">${link.name || 'Link'}</mj-navbar-link>`
                ).join('\n');
                blockMjml = blockMjml.replace('{{navLinksArea}}', linksHtml);
              } else if (key === 'social_icons' || key === 'socialLinks') {
                const iconsHtml = value.map((icon: any) =>
                  `<mj-social-element name="${icon.platform?.toLowerCase() || 'share'}" href="${icon.url || '#'}"></mj-social-element>`
                ).join('\n');
                blockMjml = blockMjml.replace('{{socialIconsArea}}', iconsHtml);
              }
            } else {
              // More efficient single replacement (no array of placeholders)
              const stringValue = String(value);
              blockMjml = blockMjml.replaceAll(placeholder, stringValue);

              // Only try alternative formats if there's a good chance they exist
              if (blockMjml.includes('{{')) {
                // Convert camelCase to snake_case for placeholder check if needed
                const camelCasePlaceholder = `{{${key.replace(/_([a-z])/g, g => g[1].toUpperCase())}}}`;
                const snakeCasePlaceholder = `{{${key.replace(/[A-Z]/g, letter => `_${letter.toLowerCase()}`)}}}`;

                blockMjml = blockMjml.replaceAll(camelCasePlaceholder, stringValue);
                blockMjml = blockMjml.replaceAll(snakeCasePlaceholder, stringValue);
              }
            }
          }
        });

        // More efficient placeholder cleanup - only if needed
        if (blockMjml.includes('{{')) {
          blockMjml = blockMjml.replace(/\{\{[\w.-]+\}\}/g, '');
        }

        return blockMjml;
      }).join('\n');

      mjmlBodyContent += chunkContent;
    }

    // Use template state for colors/fonts, falling back to defaults
    const brandColors = template?.brandColors ?? {};
    const defaultFonts = template?.defaultFonts ?? {};
    const primaryColor = brandColors.primary || '#4F46E5';
    const backgroundColor = brandColors.background || '#f3f4f6';
    const textColor = brandColors.text || '#111827';
    const headingFont = defaultFonts.heading || 'Arial, sans-serif';
    const bodyFont = defaultFonts.body || 'Arial, sans-serif';

    // Construct the full MJML document
    const fullMjml = `
<mjml>
  <mj-head>
    <mj-title>${template?.subject || 'Email Template'}</mj-title>
    <mj-font name="Roboto" href="https://fonts.googleapis.com/css?family=Roboto:300,400,500,700" />
    <mj-attributes>
      <mj-all padding="0px" font-family="${bodyFont}" />
      <mj-text padding="10px 25px" font-size="14px" line-height="1.5" color="${textColor}" />
      <mj-section padding="10px 0" />
      <mj-column padding="5px" />
      <mj-button background-color="${primaryColor}" color="#ffffff" font-weight="bold" border-radius="4px" padding="10px 20px" />
      <mj-image padding="0px" />
    </mj-attributes>
    <mj-style inline="inline">
      /* Inline styles */
      a { color: ${primaryColor} !important; text-decoration: none !important; }
    </mj-style>
     <mj-style>
      /* Embedded styles */
      .hover-link:hover { text-decoration: underline !important; }
    </mj-style>
  </mj-head>
  <mj-body background-color="${backgroundColor}">
    ${mjmlBodyContent}
  </mj-body>
</mjml>`;

    // Store in cache
    templateCache.set(cacheKey, {
      mjml: fullMjml,
      html: '', // Will be populated on first HTML conversion
      timestamp: Date.now()
    });

    return fullMjml;
  }, [template]);

  useEffect(() => {
    const generatePreview = () => {
      if (!editorBlocks || editorBlocks.length === 0) {
        setPreviewHtml('<div style="display: flex; justify-content: center; align-items: center; height: 100%; color: grey; padding: 20px; text-align: center;">Drag blocks here to build your email</div>');
        return;
      }

      try {
        // Generate cache key
        const cacheKey = generateCacheKey(editorBlocks);

        // Check for valid cached HTML
        if (templateCache.has(cacheKey)) {
          const cached = templateCache.get(cacheKey);
          if (cached && cached.html && (Date.now() - cached.timestamp) < CACHE_TTL) {
            console.log("[EmailEditor Preview] Using cached HTML");
            setPreviewHtml(cached.html);
            return;
          }
        }

        // If we get here, generate a new preview
        const mjmlString = generateMjmlFromBlocks(editorBlocks);
        console.log("[EmailEditor Preview] Generating fresh HTML preview");

        // Use a web worker for MJML conversion if possible
        if (window.Worker) {
          // Note: You would need to create a separate mjml-worker.js file
          // This is just demonstrating the concept
          const workerTimeout = setTimeout(() => {
            // Fallback if worker takes too long
            try {
              const { html, errors } = mjml2html(mjmlString, { validationLevel: 'soft' });
              setPreviewHtml(html);
              // Update cache
              const cacheKey = generateCacheKey(editorBlocks);
              if (templateCache.has(cacheKey)) {
                const entry = templateCache.get(cacheKey);
                if (entry) {
                  entry.html = html;
                  entry.timestamp = Date.now();
                }
              }
            } catch (fallbackErr: any) {
              console.error('[EmailEditor Preview] Error in fallback conversion:', fallbackErr);
              setPreviewHtml(`<div style="padding: 20px; color: red;">Preview Error: ${fallbackErr?.message || 'Unknown error'}</div>`);
            }
          }, 1000); // 1 second timeout

          // This is just conceptual - actual implementation would need the worker file
          // const worker = new Worker('/mjml-worker.js');
          // worker.postMessage(mjmlString);
          // worker.onmessage = (e) => {
          //   clearTimeout(workerTimeout);
          //   const { html, errors } = e.data;
          //   setPreviewHtml(html);
          //   // Update cache
          //   updateHtmlCache(cacheKey, html);
          // };
        } else {
          // Direct conversion when Web Workers aren't available
          const { html, errors } = mjml2html(mjmlString, { validationLevel: 'soft' });
          if (errors && errors.length > 0) {
            console.warn('[EmailEditor Preview] MJML Validation:', errors.length, 'issues');
          }
          setPreviewHtml(html);

          // Update cache
          if (templateCache.has(cacheKey)) {
            const entry = templateCache.get(cacheKey);
            if (entry) {
              entry.html = html;
              entry.timestamp = Date.now();
            }
          }
        }
      } catch (err: any) {
        console.error('[EmailEditor Preview] Error generating preview:', err);
        setPreviewHtml(`<div style="padding: 20px; color: red;">Preview Error: ${err?.message || 'Unknown error'}</div>`);
      }
    };

    // Debounce the preview generation to avoid too many updates
    const debounceTimeout = setTimeout(generatePreview, 300);
    return () => clearTimeout(debounceTimeout);
  }, [editorBlocks, generateMjmlFromBlocks]);

  // --- Enhanced DND Callbacks --- //
  const moveBlock = useCallback((dragIndex: number, hoverIndex: number) => {
      setEditorBlocks(prevBlocks => {
          const newBlocks = [...prevBlocks];
          const [draggedBlock] = newBlocks.splice(dragIndex, 1);
          newBlocks.splice(hoverIndex, 0, draggedBlock);

          // Save to history for undo/redo
          saveToHistory(newBlocks, selectedBlockIndex === dragIndex ? hoverIndex : selectedBlockIndex);

          return newBlocks;
      });

      // Adjust selected index
      if (selectedBlockIndex === dragIndex) {
          setSelectedBlockIndex(hoverIndex);
      } else if (selectedBlockIndex !== null) {
          if (dragIndex < hoverIndex && selectedBlockIndex > dragIndex && selectedBlockIndex <= hoverIndex) {
              setSelectedBlockIndex(s => (s !== null ? s - 1 : null));
          } else if (dragIndex > hoverIndex && selectedBlockIndex >= hoverIndex && selectedBlockIndex < dragIndex) {
              setSelectedBlockIndex(s => (s !== null ? s + 1 : null));
          }
      }
  }, [selectedBlockIndex, saveToHistory]);

  const dropBlockFromLibrary = useCallback((block: any, index: number) => {
      // Cast the block to ensure it has all required properties
      const blockWithRequiredProps = {
          ...JSON.parse(JSON.stringify(block)),
          blockId: block.blockId || block._id || `block-${Date.now()}`, // Ensure blockId is never undefined
          content: block.content ? JSON.parse(JSON.stringify(block.content)) : {},
          instanceId: `${block.blockId || block._id || 'new'}-${Date.now()}-${Math.random().toString(36).substring(7)}`
      };

      setEditorBlocks(prevBlocks => {
          const newBlocks = [...prevBlocks];
          newBlocks.splice(index, 0, blockWithRequiredProps as Block);

          // Save to history for undo/redo
          saveToHistory(newBlocks, index);

          return newBlocks;
      });
      setSelectedBlockIndex(index); // Select the newly dropped block
  }, [saveToHistory]);

  const removeBlock = useCallback((index: number) => {
      setEditorBlocks(prevBlocks => {
          const newBlocks = prevBlocks.filter((_, i) => i !== index);

          // Save to history for undo/redo
          saveToHistory(newBlocks, selectedBlockIndex === index ? null :
            (selectedBlockIndex !== null && selectedBlockIndex > index ? selectedBlockIndex - 1 : selectedBlockIndex));

          return newBlocks;
      });

      if (selectedBlockIndex === index) {
          setSelectedBlockIndex(null);
      } else if (selectedBlockIndex !== null && selectedBlockIndex > index) {
          setSelectedBlockIndex(prevIndex => (prevIndex !== null ? prevIndex - 1 : null));
      }
  }, [selectedBlockIndex, saveToHistory]);

  const duplicateBlock = useCallback((index: number) => {
      setEditorBlocks(prevBlocks => {
          const blockToDuplicate = prevBlocks[index];
          const duplicatedBlock = {
              ...JSON.parse(JSON.stringify(blockToDuplicate)),
              instanceId: `${blockToDuplicate.blockId || blockToDuplicate._id || 'dup'}-${Date.now()}-${Math.random().toString(36).substring(7)}`
          };

          const newBlocks = [...prevBlocks];
          newBlocks.splice(index + 1, 0, duplicatedBlock);

          // Save to history for undo/redo
          saveToHistory(newBlocks, index + 1);

          return newBlocks;
      });
      setSelectedBlockIndex(index + 1); // Select the duplicated block
  }, [saveToHistory]);

  const updateBlockContent = useCallback((index: number, updatedContent: Partial<BlockContent>) => {
      setEditorBlocks(prevBlocks => {
          const newBlocks = prevBlocks.map((block, i) =>
              i === index
                  ? { ...block, content: { ...(block.content || {}), ...updatedContent } }
                  : block
          );

          // Save to history for undo/redo (debounced to avoid too many history entries)
          const debouncedSave = setTimeout(() => {
              saveToHistory(newBlocks, selectedBlockIndex);
          }, 1000);

          return newBlocks;
      });
  }, [saveToHistory, selectedBlockIndex]);

  // --- Save Handler --- //
  const handleSave = async (templateNameToSave: string, isPublicStatus: boolean = false) => {
    setIsSaving(true);
    setSaveError(null);
    try {
      const mjml = generateMjmlFromBlocks(editorBlocks);
      const { html, errors: conversionErrors } = mjml2html(mjml);
      if (conversionErrors && conversionErrors.length > 0) {
        console.warn('MJML conversion errors detected during save:', conversionErrors);
        // Consider not saving if critical errors occurred
      }

      const blockIds = editorBlocks
        .map(b => String(b.blockId || b._id)) // Ensure string ID
        .filter(Boolean); // Filter out any potential undefined/null

      const payload: Partial<Template> & { templateId?: string; blockIds: string[] } = {
        templateId: template?._id,
        templateName: templateNameToSave,
        mjml,
        html,
        blockIds,
        subject: template?.subject || 'Untitled Template',
        tags: template?.tags || [],
        isPublic: isPublicStatus,
        description: template?.description || '',
        aiPrompt: template?.aiPrompt || '',
        isAiGenerated: template?.isAiGenerated || false
      };

      // Add userId if it's a new template - CRITICAL: Ensure req.user is populated in backend
      // This assumes the backend will get the userId from the authenticated request (authenticateJWT)
      // if (!payload.templateId) {
      //   payload.userId = template?.userId; // Might be set from prefs initially
      // }

      const response = await api.post<TemplateSaveResponse>('/templates/save', payload);
      const savedTemplate = response.data.template;
      setTemplate(savedTemplate);

      // Re-sync editor blocks if necessary (e.g., if backend modifies content/IDs)
      // This part needs careful implementation if backend modifies block content on save
      if (savedTemplate.blockIds && availableBlocks.length > 0) {
         const populatedBlocks = savedTemplate.blockIds.map((id, index) => {
            const foundBlockDef = availableBlocks.find(b => b.blockId === id || b._id === id);
            const instanceContent = savedTemplate.blocks?.[index]?.content || foundBlockDef?.content || {};
            const instanceId = `${id}-${index}-${Date.now()}-${Math.random().toString(36).substring(7)}`;
            return foundBlockDef ? { ...foundBlockDef, content: { ...instanceContent }, instanceId } : null;
         }).filter((b) => b !== null) as Block[];
         setEditorBlocks(populatedBlocks);
      }

      setShowSaveModal(false);
      console.log('Template saved successfully!');
      if (!templateId && savedTemplate._id) {
        navigate(`/email-editor/${savedTemplate._id}`, { replace: true });
      }
      return savedTemplate;
    } catch (err: any) {
      console.error('Error saving template:', err);
      const errorMessage = err.response?.data?.error || err.message || 'Failed to save template.';
      setSaveError(errorMessage);
      return null;
    } finally {
      setIsSaving(false);
    }
  };

  // --- Drop Target for Canvas --- //
  const [{ isOverCanvas }, drop] = useDrop(() => ({
      accept: [ItemTypes.BLOCK, ItemTypes.LIBRARY_BLOCK],
      drop: (item: EditorDragItem | LibraryDragItem, monitor) => {
          if (monitor.didDrop()) return;

          const editorDiv = editorCanvasRef.current;
          const clientOffset = monitor.getClientOffset();
          if (!clientOffset || !editorDiv) return;

          const hoverIndex = findDropIndex(clientOffset.y, editorDiv);

          if (item.type === ItemTypes.LIBRARY_BLOCK) {
            dropBlockFromLibrary((item as LibraryDragItem).block, hoverIndex);
          }
          // Reordering is handled by DraggableBlock's hover
      },
      collect: monitor => ({
        isOverCanvas: monitor.isOver({ shallow: true }),
      }),
  }), [editorBlocks, moveBlock, dropBlockFromLibrary]); // Ensure correct dependencies

  // Helper function to find the correct drop index
  const findDropIndex = (clientY: number | undefined, container: HTMLDivElement): number => {
      if (clientY === undefined) return editorBlocks.length;

      const containerRect = container.getBoundingClientRect();
      const offsetY = clientY - containerRect.top + container.scrollTop;

      let calculatedIndex = editorBlocks.length;
      const children = Array.from(container.children) as HTMLElement[];

      for (let i = 0; i < children.length; i++) {
          const child = children[i];
          if (!child.classList || !child.classList.contains('draggable-block')) continue;

          const childTop = child.offsetTop;
          const childHeight = child.offsetHeight;
          const middleY = childTop + childHeight / 2;

          if (offsetY < middleY) {
              calculatedIndex = i;
              break;
          }
      }
      return calculatedIndex;
  };

  // Cleanup cache when component unmounts
  useEffect(() => {
    return () => {
      // Clear the entire cache when this editor instance is unmounted
      templateCache.clear();
    };
  }, []);

  // Add a cleanup function for memory management
  const cleanupUnusedResources = useCallback(() => {
    // Clear any unused thumbnails from memory if browser gets low on memory
    if ('memory' in performance) {
      const memoryInfo = (performance as any).memory;
      if (memoryInfo.usedJSHeapSize > memoryInfo.jsHeapSizeLimit * 0.8) {
        console.log('[EmailEditor] Memory pressure detected, cleaning up resources');
        // Force a garbage collection if possible
        clearStaleCache();
        // You could also unload any non-visible thumbnails or other large objects
      }
    }
  }, []);

  // Listen for low memory events
  useEffect(() => {
    if ('onmemorywarning' in window) {
      (window as any).addEventListener('memorywarning', cleanupUnusedResources);
      return () => {
        (window as any).removeEventListener('memorywarning', cleanupUnusedResources);
      };
    }
  }, [cleanupUnusedResources]);

  // --- Render Logic --- //
  if (isLoading) {
    return <div className="flex justify-center items-center h-screen text-gray-600 text-lg">Loading Editor...</div>;
  }

  // Critical error on initial load
  if (error && !template) {
    return <div className="p-4 m-4 text-red-700 bg-red-100 border border-red-400 rounded">Error loading editor configuration: {error}</div>;
  }

  return (
    <div className="email-editor">
      {/* Enhanced Toolbar with Driftly Design */}
      <div className="editor-toolbar">
        <div className="toolbar-left">
          {/* Logo and Title */}
          <div className="flex items-center space-x-3">
            <div className="flex items-center space-x-2">
              <div className="w-8 h-8 rounded-lg bg-gradient-to-br from-primary-blue to-accent-coral flex items-center justify-center">
                <svg className="w-5 h-5 text-white" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                  <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M3 8l7.89 5.26a2 2 0 002.22 0L21 8M5 19h14a2 2 0 002-2V7a2 2 0 00-2-2H5a2 2 0 00-2 2v10a2 2 0 002 2z" />
                </svg>
              </div>
              <h1 className="text-lg font-semibold text-text-primary">Email Designer</h1>
            </div>

            {/* Undo/Redo buttons */}
            <div className="flex items-center space-x-1 ml-6">
              <button
                onClick={undo}
                disabled={!canUndo}
                className="p-2 rounded-md transition-all duration-200 disabled:opacity-50 disabled:cursor-not-allowed"
                style={{
                  color: canUndo ? 'var(--color-text-primary)' : 'var(--color-text-secondary)',
                  background: canUndo ? 'rgba(79, 148, 221, 0.1)' : 'rgba(31, 41, 55, 0.3)'
                }}
                title="Undo (Ctrl+Z)"
              >
                <svg className="w-5 h-5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                  <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M3 10h10a8 8 0 018 8v2M3 10l6 6m-6-6l6-6" />
                </svg>
              </button>
              <button
                onClick={redo}
                disabled={!canRedo}
                className="p-2 rounded-md transition-all duration-200 disabled:opacity-50 disabled:cursor-not-allowed"
                style={{
                  color: canRedo ? 'var(--color-text-primary)' : 'var(--color-text-secondary)',
                  background: canRedo ? 'rgba(79, 148, 221, 0.1)' : 'rgba(31, 41, 55, 0.3)'
                }}
                title="Redo (Ctrl+Y)"
              >
                <svg className="w-5 h-5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                  <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M21 10h-10a8 8 0 00-8 8v2m18-10l-6 6m6-6l-6-6" />
                </svg>
              </button>
            </div>
          </div>
        </div>

        <div className="toolbar-center">
          {/* View Mode Toggle */}
          <div className="preview-mode-toggle">
            <button
              onClick={() => setViewMode('edit')}
              className={`toggle-button ${viewMode === 'edit' ? 'active' : ''}`}
            >
              <svg className="icon w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M11 5H6a2 2 0 00-2 2v11a2 2 0 002 2h11a2 2 0 002-2v-5m-1.414-9.414a2 2 0 112.828 2.828L11.828 15H9v-2.828l8.586-8.586z" />
              </svg>
              Edit
            </button>
            <button
              onClick={() => setViewMode('split')}
              className={`toggle-button ${viewMode === 'split' ? 'active' : ''}`}
            >
              <svg className="icon w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M9 17V7m0 10a2 2 0 01-2 2H5a2 2 0 01-2-2V7a2 2 0 012-2h2a2 2 0 012 2m0 10a2 2 0 002 2h2a2 2 0 002-2M9 7a2 2 0 012-2h2a2 2 0 012 2m0 10V7m0 10a2 2 0 002 2h2a2 2 0 002-2V7a2 2 0 00-2-2h-2a2 2 0 00-2 2" />
              </svg>
              Split
            </button>
            <button
              onClick={() => setViewMode('preview')}
              className={`toggle-button ${viewMode === 'preview' ? 'active' : ''}`}
            >
              <svg className="icon w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M15 12a3 3 0 11-6 0 3 3 0 016 0z" />
                <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M2.458 12C3.732 7.943 7.523 5 12 5c4.478 0 8.268 2.943 9.542 7-1.274 4.057-5.064 7-9.542 7-4.477 0-8.268-2.943-9.542-7z" />
              </svg>
              Preview
            </button>
          </div>
        </div>

        <div className="toolbar-right">
          {/* Preview Mode Toggle */}
          <div className="preview-mode-toggle">
            <button
              onClick={() => setPreviewMode('desktop')}
              className={`toggle-button ${previewMode === 'desktop' ? 'active' : ''}`}
            >
              <svg className="icon w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M9.75 17L9 20l-1 1h8l-1-1-.75-3M3 13h18M5 17h14a2 2 0 002-2V5a2 2 0 00-2-2H5a2 2 0 00-2 2v10a2 2 0 002 2z" />
              </svg>
              Desktop
            </button>
            <button
              onClick={() => setPreviewMode('mobile')}
              className={`toggle-button ${previewMode === 'mobile' ? 'active' : ''}`}
            >
              <svg className="icon w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M12 18h.01M8 21h8a1 1 0 001-1V4a1 1 0 00-1-1H8a1 1 0 00-1 1v16a1 1 0 001 1z" />
              </svg>
              Mobile
            </button>
          </div>

          {/* Save Button */}
          <button
            onClick={() => {
              setTemplate(prev => ({ ...(prev || {}), templateName: prev?.templateName || 'Untitled Template' } as Template));
              setSaveError(null);
              setShowSaveModal(true);
            }}
            disabled={isSaving}
            className="save-button"
          >
            {isSaving ? (
              <>
                <svg className="w-4 h-4 mr-2 animate-spin" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                  <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M4 4v5h.582m15.356 2A8.001 8.001 0 004.582 9m0 0H9m11 11v-5h-.581m0 0a8.003 8.003 0 01-15.357-2m15.357 2H15" />
                </svg>
                Saving...
              </>
            ) : (
              <>
                <svg className="w-4 h-4 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                  <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M8 7H5a2 2 0 00-2 2v9a2 2 0 002 2h14a2 2 0 002-2V9a2 2 0 00-2-2h-3m-1 4l-3 3m0 0l-3-3m3 3V4" />
                </svg>
                Save Template
              </>
            )}
          </button>
        </div>
      </div>

      {/* Display non-critical error (e.g., failed suggestion) */}
      {error && templateId &&
          <div className="p-2 text-sm text-red-700 bg-red-100 border-b border-red-300 text-center">Error: {error}</div>
      }

      <div className="editor-content">
        {/* Enhanced Block Library Panel */}
        <div className="block-library-panel">
          <h3>Block Library</h3>
          <BlockLibrary
            blocks={availableBlocks}
            onAddBlock={(block: Block) => dropBlockFromLibrary(block, editorBlocks.length)}
          />
        </div>

        {/* Enhanced Editor Workspace / Canvas */}
        <div ref={drop} className={`editor-workspace ${isOverCanvas ? 'drop-zone-active' : ''}`}>
          <div ref={editorCanvasRef} className="blocks-container">
            {editorBlocks.length === 0 ? (
              <div className="empty-blocks">
                <p>Drag blocks from the library here<br/>or click a block in the library to add it.</p>
              </div>
            ) : (
              editorBlocks.map((block, index) => (
                <DraggableBlock
                  key={block.instanceId}
                  block={block}
                  index={index}
                  moveBlock={moveBlock}
                  removeBlock={removeBlock}
                  duplicateBlock={duplicateBlock}
                  isSelected={selectedBlockIndex === index}
                  onClick={() => setSelectedBlockIndex(index)}
                />
              ))
            )}
          </div>
        </div>

        {/* Enhanced Right Panel (Toggles Preview/Editor) */}
        {(selectedBlockIndex === null || editorBlocks.length === 0) ? (
            // Show Preview when no block is selected OR if editor is empty
            <div className="preview-panel">
                <h3>Preview</h3>
                <EmailPreview
                    html={previewHtml}
                    mode={previewMode}
                    iframeRef={previewIframeRef}
                />
            </div>
        ) : (
            // Show Block Editor when a block is selected
            <div className="block-editor-panel">
                <h3 className="flex justify-between items-center">
                  <span>Edit: {editorBlocks[selectedBlockIndex]?.name || 'Block'}</span>
                  <button
                    onClick={() => setSelectedBlockIndex(null)}
                    className="close-button"
                    title="Close Editor"
                  >
                    <svg className="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                      <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M6 18L18 6M6 6l12 12" />
                    </svg>
                  </button>
                </h3>
                <div className="block-editor">
                    {editorBlocks[selectedBlockIndex] && (
                        <BlockEditor
                            block={editorBlocks[selectedBlockIndex]}
                            onUpdate={(content) => {
                                if (selectedBlockIndex !== null) {
                                    updateBlockContent(selectedBlockIndex, content);
                                }
                            }}
                        />
                    )}
                </div>
            </div>
        )}

      </div>

      {/* Save Modal */}
      {showSaveModal && (
        <SaveTemplateModal
          initialName={template?.templateName || 'Untitled Template'}
          onSave={handleSave}
          onCancel={() => { setShowSaveModal(false); setSaveError(null); }}
          isSaving={isSaving}
          error={saveError} // Pass save error to modal
        />
      )}
    </div>
  );
};

// Draggable Block Component (Internal to EmailEditor)
interface DraggableBlockProps {
  block: Block;
  index: number;
  moveBlock: (dragIndex: number, hoverIndex: number) => void;
  removeBlock: (index: number) => void;
  duplicateBlock: (index: number) => void;
  isSelected: boolean;
  onClick: () => void;
}

const DraggableBlock: React.FC<DraggableBlockProps> = ({
  block,
  index,
  moveBlock,
  removeBlock,
  duplicateBlock,
  isSelected,
  onClick
}) => {
  const ref = useRef<HTMLDivElement>(null);

  const [{ handlerId }, drop] = useDrop<EditorDragItem, void, { handlerId: string | symbol | null }>(() => ({
    accept: ItemTypes.BLOCK,
    hover: (item: EditorDragItem, monitor: DropTargetMonitor<EditorDragItem, void>) => {
      if (!ref.current) return;
      const dragIndex = item.index;
      const hoverIndex = index;
      if (dragIndex === hoverIndex) return;

      const hoverBoundingRect = ref.current.getBoundingClientRect();
      const hoverMiddleY = (hoverBoundingRect.bottom - hoverBoundingRect.top) / 2;
      const clientOffset = monitor.getClientOffset();
      if (!clientOffset) return;
      const hoverClientY = clientOffset.y - hoverBoundingRect.top;

      if (dragIndex < hoverIndex && hoverClientY < hoverMiddleY) return;
      if (dragIndex > hoverIndex && hoverClientY > hoverMiddleY) return;

      moveBlock(dragIndex, hoverIndex);
      item.index = hoverIndex; // Mutate monitor item for performance
    },
    collect: (monitor) => ({
        handlerId: monitor.getHandlerId(),
    }),
  }), [index, moveBlock]); // Dependencies for useDrop hover logic

  const [{ isDragging }, drag] = useDrag(() => ({
    type: ItemTypes.BLOCK,
    item: { index, id: block.instanceId, type: ItemTypes.BLOCK }, // Return item as a function
    collect: (monitor) => ({
      isDragging: monitor.isDragging()
    })
  }), [index, block.instanceId]); // Dependencies for useDrag

  drag(drop(ref)); // Combine drag and drop refs

  return (
    <div
      ref={ref}
      data-handler-id={handlerId}
      className={`draggable-block ${isDragging ? 'dragging' : ''} ${isSelected ? 'selected' : ''}`}
      onClick={onClick}
    >
      <div className="block-header">
        <span className="block-type" title={block.name}>
          {block.name} <span style={{ color: 'var(--color-text-secondary)' }}>({block.category})</span>
        </span>
        <div className="block-actions">
           {/* Duplicate Button */}
           <button
            type="button"
            className="duplicate-block"
            onClick={(e) => {
                e.stopPropagation();
                duplicateBlock(index);
            }}
            title="Duplicate Block"
           >
               <svg className="h-4 w-4" fill="none" viewBox="0 0 24 24" stroke="currentColor" strokeWidth={2}>
                   <path strokeLinecap="round" strokeLinejoin="round" d="M8 16H6a2 2 0 01-2-2V6a2 2 0 012-2h8a2 2 0 012 2v2m-6 12h8a2 2 0 002-2v-8a2 2 0 00-2-2h-8a2 2 0 00-2 2v8a2 2 0 002 2z" />
               </svg>
           </button>
           {/* Remove Button */}
          <button
            type="button"
            className="remove-block"
            onClick={(e) => {
              e.stopPropagation();
              removeBlock(index);
            }}
            title="Remove Block"
          >
            <svg className="h-4 w-4" fill="none" viewBox="0 0 24 24" stroke="currentColor" strokeWidth={2}>
                <path strokeLinecap="round" strokeLinejoin="round" d="M6 18L18 6M6 6l12 12" />
            </svg>
          </button>
        </div>
      </div>
      {/* Enhanced preview inside the draggable block */}
      <div className="block-content-preview">
        {(() => {
          // Display appropriate preview based on block type
          if (block.blockId === 'header/simple-nav') {
            return '🧭 Navigation Header';
          } else if (block.blockId === 'layout/hero') {
            return `🎯 ${block.content?.heroHeadline || 'Hero Section'}`;
          } else if (block.blockId === 'content/headline') {
            return `📰 ${block.content?.headline || 'Headline'}`;
          } else if (block.blockId === 'product/grid') {
            return '🛍️ Product Grid: ' + [
              block.content?.prod1_name,
              block.content?.prod2_name,
              block.content?.prod3_name
            ].filter(Boolean).join(', ');
          } else if (block.blockId === 'cta/button') {
            return `🔘 ${block.content?.buttonText || 'Button'}`;
          } else if (block.blockId === 'footer/standard') {
            return `🦶 ${block.content?.companyName ? `Footer: ${block.content.companyName}` : 'Standard Footer'}`;
          } else {
            // Fallback to original logic
            return block.content?.headline ||
                   block.content?.body?.substring(0, 50) +
                   (block.content?.body && block.content.body.length > 50 ? '...' : '') ||
                   `📦 ${block.name}`;
          }
        })()}
        {block.thumbnail && (
          <img
            src={block.thumbnail}
            alt={`${block.name} thumbnail`}
            className="mx-auto h-12 mt-3 opacity-75 object-contain rounded"
          />
        )}
      </div>
    </div>
  );
};

export { EmailEditor };
export default EmailEditor;